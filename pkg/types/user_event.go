package types

import "encoding/json"

const (
	UserEventConnect        = "sdkConnect"
	UserEventDisconnect     = "sdkDisconnect"
	UserEventSdkNotFound    = "sdkNotFound"
	UserEventCtlUnavailable = "ctlUnavailable"
)

const (
	UserEventTopic = "userEvent"
)

type UserEvent struct {
	Name      string `json:"name"`
	SdkId     string `json:"sdkId"`
	SdkIp     string `json:"sdkIp"`
	Host      string `json:"host"`
	Supplier  string `json:"supplier"`
	ConnId    string `json:"connId"`
	CtlConnId string `json:"ctlConnId"`
	TimeStamp int64  `json:"timestamp"` // 毫秒时间戳
	Meta      string `json:"meta"`
	Sequence  int64  `json:"sequence"`  // 事件序列号，用于确保事件顺序
}

func NewUserEvent(Name string, SdkId string, SdkIp string, Host string, Supplier string, ConnId string, TimeStamp int64) *UserEvent {
	return &UserEvent{
		Name:     Name,
		SdkId:    SdkId,
		SdkIp:    SdkIp,
		Host:     Host,
		Supplier: Supplier,
		ConnId:   ConnId,
	}
}

func (ue *UserEvent) ToMQMessage() *MQMessage {
	return NewMQMessage("userEvent", generateTraceID(), ue.Name)
}

func (ue *UserEvent) MarshalStr() string {
	data, _ := json.Marshal(ue)
	return string(data)
}

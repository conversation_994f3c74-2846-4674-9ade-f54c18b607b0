package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"sh_proxy/pkg/types"
)

// DeviceInfo 旧格式的设备信息
type DeviceInfo struct {
	SdkIP     string                       `json:"sdkIp"`
	Supplier  string                       `json:"supplier"`
	Location  string                       `json:"location"`
	SdkConn   map[string][]*types.ConnInfo `json:"sdkConn"`
	LastSeen  time.Time                    `json:"lastSeen"`
	CreatedAt time.Time                    `json:"createdAt"`
}

// HashConnInfo Hash存储的连接信息
type HashConnInfo struct {
	SdkId     string `json:"sdkId"`
	Host      string `json:"host"`
	ConnId    string `json:"connId"`
	CtlConnId string `json:"ctlConnId"`
	Timestamp int64  `json:"timestamp"`
}

// ToJSON 转换为JSON字符串
func (h *HashConnInfo) ToJSON() string {
	data, _ := json.Marshal(h)
	return string(data)
}

// DeviceMeta 设备元信息
type DeviceMeta struct {
	SdkIP     string `json:"sdkIp"`
	Supplier  string `json:"supplier"`
	Location  string `json:"location"`
	LastSeen  int64  `json:"lastSeen"`
	CreatedAt int64  `json:"createdAt"`
}

// MigrationTool 数据迁移工具
type MigrationTool struct {
	rdb *redis.Client
}

// NewMigrationTool 创建迁移工具
func NewMigrationTool(redisAddr string) *MigrationTool {
	rdb := redis.NewClient(&redis.Options{
		Addr: redisAddr,
	})
	
	return &MigrationTool{
		rdb: rdb,
	}
}

// MigrateDevices 迁移设备数据从JSON格式到Hash格式
func (m *MigrationTool) MigrateDevices(ctx context.Context, dryRun bool) error {
	// 扫描所有设备键
	pattern := "device:*"
	iter := m.rdb.Scan(ctx, 0, pattern, 0).Iterator()
	
	migratedCount := 0
	skippedCount := 0
	errorCount := 0
	
	for iter.Next(ctx) {
		deviceKey := iter.Val()
		
		// 跳过元信息键
		if strings.HasSuffix(deviceKey, ":meta") {
			continue
		}
		
		// 检查是否已经是Hash格式
		keyType, err := m.rdb.Type(ctx, deviceKey).Result()
		if err != nil {
			log.Printf("Error checking key type for %s: %v", deviceKey, err)
			errorCount++
			continue
		}
		
		if keyType == "hash" {
			log.Printf("Skipping already migrated device: %s", deviceKey)
			skippedCount++
			continue
		}
		
		if keyType != "string" {
			log.Printf("Unexpected key type %s for device: %s", keyType, deviceKey)
			skippedCount++
			continue
		}
		
		// 迁移单个设备
		if err := m.migrateDevice(ctx, deviceKey, dryRun); err != nil {
			log.Printf("Error migrating device %s: %v", deviceKey, err)
			errorCount++
		} else {
			migratedCount++
			log.Printf("Successfully migrated device: %s", deviceKey)
		}
	}
	
	if err := iter.Err(); err != nil {
		return fmt.Errorf("error scanning devices: %w", err)
	}
	
	log.Printf("Migration completed: migrated=%d, skipped=%d, errors=%d", 
		migratedCount, skippedCount, errorCount)
	
	return nil
}

// migrateDevice 迁移单个设备
func (m *MigrationTool) migrateDevice(ctx context.Context, deviceKey string, dryRun bool) error {
	// 1. 读取旧格式数据
	data, err := m.rdb.Get(ctx, deviceKey).Result()
	if err != nil {
		return fmt.Errorf("failed to get device data: %w", err)
	}
	
	var device DeviceInfo
	if err := json.Unmarshal([]byte(data), &device); err != nil {
		return fmt.Errorf("failed to unmarshal device data: %w", err)
	}
	
	log.Printf("Migrating device: %s, location: %s, connections: %d", 
		deviceKey, device.Location, m.countConnections(&device))
	
	if dryRun {
		log.Printf("DRY RUN: Would migrate device %s", deviceKey)
		return nil
	}
	
	// 2. 创建Hash格式数据
	metaKey := deviceKey + ":meta"
	
	// 使用事务确保原子性
	pipe := m.rdb.TxPipeline()
	
	// 3. 设置设备元信息
	deviceMeta := &DeviceMeta{
		SdkIP:     device.SdkIP,
		Supplier:  device.Supplier,
		Location:  device.Location,
		LastSeen:  device.LastSeen.Unix(),
		CreatedAt: device.CreatedAt.Unix(),
	}
	
	pipe.HMSet(ctx, metaKey, map[string]interface{}{
		"sdkIp":     deviceMeta.SdkIP,
		"supplier":  deviceMeta.Supplier,
		"location":  deviceMeta.Location,
		"lastSeen":  deviceMeta.LastSeen,
		"createdAt": deviceMeta.CreatedAt,
	})
	
	// 4. 转换连接信息到Hash
	for sdkId, connections := range device.SdkConn {
		for _, conn := range connections {
			fieldKey := fmt.Sprintf("%s:%s:%s", conn.CtlConnId, sdkId, conn.ConnId)
			
			hashConn := &HashConnInfo{
				SdkId:     sdkId,
				Host:      conn.Host,
				ConnId:    conn.ConnId,
				CtlConnId: conn.CtlConnId,
				Timestamp: conn.Timestamp,
			}
			
			pipe.HSet(ctx, deviceKey, fieldKey, hashConn.ToJSON())
		}
	}
	
	// 5. 删除旧的JSON数据（在设置新数据后）
	pipe.Del(ctx, deviceKey+"_old")
	pipe.Rename(ctx, deviceKey, deviceKey+"_old")
	
	// 6. 执行事务
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute migration transaction: %w", err)
	}
	
	// 7. 验证迁移结果
	if err := m.verifyMigration(ctx, deviceKey, &device); err != nil {
		log.Printf("Migration verification failed for %s: %v", deviceKey, err)
		// 回滚
		m.rdb.Rename(ctx, deviceKey+"_old", deviceKey)
		return fmt.Errorf("migration verification failed: %w", err)
	}
	
	// 8. 删除备份
	m.rdb.Del(ctx, deviceKey+"_old")
	
	return nil
}

// verifyMigration 验证迁移结果
func (m *MigrationTool) verifyMigration(ctx context.Context, deviceKey string, originalDevice *DeviceInfo) error {
	// 读取迁移后的数据
	connMap, err := m.rdb.HGetAll(ctx, deviceKey).Result()
	if err != nil {
		return fmt.Errorf("failed to read migrated connections: %w", err)
	}
	
	metaKey := deviceKey + ":meta"
	metaMap, err := m.rdb.HGetAll(ctx, metaKey).Result()
	if err != nil {
		return fmt.Errorf("failed to read migrated meta: %w", err)
	}
	
	// 验证元信息
	if metaMap["location"] != originalDevice.Location {
		return fmt.Errorf("location mismatch: expected %s, got %s", 
			originalDevice.Location, metaMap["location"])
	}
	
	// 验证连接数量
	originalConnCount := m.countConnections(originalDevice)
	migratedConnCount := len(connMap)
	
	if originalConnCount != migratedConnCount {
		return fmt.Errorf("connection count mismatch: expected %d, got %d", 
			originalConnCount, migratedConnCount)
	}
	
	return nil
}

// countConnections 计算连接总数
func (m *MigrationTool) countConnections(device *DeviceInfo) int {
	count := 0
	for _, connections := range device.SdkConn {
		count += len(connections)
	}
	return count
}

// RollbackMigration 回滚迁移
func (m *MigrationTool) RollbackMigration(ctx context.Context, deviceKey string) error {
	backupKey := deviceKey + "_old"
	
	// 检查备份是否存在
	exists, err := m.rdb.Exists(ctx, backupKey).Result()
	if err != nil {
		return fmt.Errorf("failed to check backup existence: %w", err)
	}
	
	if exists == 0 {
		return fmt.Errorf("backup not found for device: %s", deviceKey)
	}
	
	// 删除新格式数据
	pipe := m.rdb.TxPipeline()
	pipe.Del(ctx, deviceKey)
	pipe.Del(ctx, deviceKey+":meta")
	
	// 恢复旧格式数据
	pipe.Rename(ctx, backupKey, deviceKey)
	
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to rollback migration: %w", err)
	}
	
	log.Printf("Successfully rolled back migration for device: %s", deviceKey)
	return nil
}

func main() {
	if len(os.Args) < 2 {
		log.Fatal("Usage: go run migrate_device_storage.go <redis_addr> [--dry-run]")
	}
	
	redisAddr := os.Args[1]
	dryRun := len(os.Args) > 2 && os.Args[2] == "--dry-run"
	
	tool := NewMigrationTool(redisAddr)
	ctx := context.Background()
	
	if dryRun {
		log.Println("Running in DRY RUN mode - no changes will be made")
	}
	
	if err := tool.MigrateDevices(ctx, dryRun); err != nil {
		log.Fatalf("Migration failed: %v", err)
	}
	
	log.Println("Migration completed successfully")
}

package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"gorm.io/driver/clickhouse"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// MongoIPInfo MongoDB中的IP信息结构
type MongoIPInfo struct {
	IP          string    `bson:"ip"`
	Country     string    `bson:"country"`
	CountryCode string    `bson:"countryCode"`
	Region      string    `bson:"region"`
	RegionName  string    `bson:"regionName"`
	City        string    `bson:"city"`
	District    string    `bson:"district"`
	Zip         string    `bson:"zip"`
	Lat         float64   `bson:"lat"`
	Lon         float64   `bson:"lon"`
	Timezone    string    `bson:"timezone"`
	Offset      int       `bson:"offset"`
	Currency    string    `bson:"currency"`
	Proxy       bool      `bson:"proxy"`
	Hosting     bool      `bson:"hosting"`
	Asname      string    `bson:"asname"`
	As          string    `bson:"as"`
	ISP         string    `bson:"isp"`
	Mobile      bool      `bson:"mobile"`
	CreatedAt   time.Time `bson:"createdAt"`
	UpdatedAt   time.Time `bson:"updatedAt"`
}

// SdkIpinfo ClickHouse中的IP信息结构
type SdkIpinfo struct {
	IP          string    `gorm:"column:ip;type:String" json:"ip"`
	Country     string    `gorm:"column:country;type:String" json:"country"`
	CountryCode string    `gorm:"column:country_code;type:String" json:"country_code"`
	Region      string    `gorm:"column:region;type:String" json:"region"`
	RegionName  string    `gorm:"column:region_name;type:String" json:"region_name"`
	City        string    `gorm:"column:city;type:String" json:"city"`
	District    string    `gorm:"column:district;type:String" json:"district"`
	Zip         string    `gorm:"column:zip;type:String" json:"zip"`
	Lat         float64   `gorm:"column:lat;type:Float64" json:"lat"`
	Lon         float64   `gorm:"column:lon;type:Float64" json:"lon"`
	Timezone    string    `gorm:"column:timezone;type:String" json:"timezone"`
	Offset      int       `gorm:"column:offset;type:Int32" json:"offset"`
	Currency    string    `gorm:"column:currency;type:String" json:"currency"`
	Proxy       bool      `gorm:"column:proxy;type:Bool" json:"proxy"`
	Hosting     bool      `gorm:"column:hosting;type:Bool" json:"hosting"`
	Asname      string    `gorm:"column:asname;type:String" json:"asname"`
	As          string    `gorm:"column:as;type:String" json:"as"`
	ISP         string    `gorm:"column:isp;type:String" json:"isp"`
	Mobile      bool      `gorm:"column:mobile;type:Bool" json:"mobile"`
	CreatedAt   time.Time `gorm:"column:created_at;type:DateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"column:updated_at;type:DateTime" json:"updated_at"`
}

// TableName 指定表名
func (SdkIpinfo) TableName() string {
	return "sdk_ipinfo"
}

// SyncConfig 同步配置
type SyncConfig struct {
	MongoURI        string
	MongoDatabase   string
	MongoCollection string
	ClickHouseDSN   string
	BatchSize       int
	DryRun          bool
}

// DataSyncer 数据同步器
type DataSyncer struct {
	config       *SyncConfig
	mongoClient  *mongo.Client
	clickhouseDB *gorm.DB
}

// NewDataSyncer 创建数据同步器
func NewDataSyncer(config *SyncConfig) (*DataSyncer, error) {
	syncer := &DataSyncer{
		config: config,
	}

	// 连接MongoDB
	if err := syncer.connectMongoDB(); err != nil {
		return nil, fmt.Errorf("failed to connect to MongoDB: %w", err)
	}

	// 连接ClickHouse
	if err := syncer.connectClickHouse(); err != nil {
		return nil, fmt.Errorf("failed to connect to ClickHouse: %w", err)
	}

	return syncer, nil
}

// connectMongoDB 连接MongoDB
func (s *DataSyncer) connectMongoDB() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	client, err := mongo.Connect(ctx, options.Client().ApplyURI(s.config.MongoURI))
	if err != nil {
		return err
	}

	// 测试连接
	if err := client.Ping(ctx, nil); err != nil {
		return err
	}

	s.mongoClient = client
	log.Printf("MongoDB连接成功: %s", s.config.MongoURI)
	return nil
}

// connectClickHouse 连接ClickHouse
func (s *DataSyncer) connectClickHouse() error {
	db, err := gorm.Open(clickhouse.Open(s.config.ClickHouseDSN), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		return err
	}

	s.clickhouseDB = db
	log.Printf("ClickHouse连接成功: %s", s.config.ClickHouseDSN)
	return nil
}

// Close 关闭连接
func (s *DataSyncer) Close() error {
	if s.mongoClient != nil {
		if err := s.mongoClient.Disconnect(context.Background()); err != nil {
			log.Printf("关闭MongoDB连接失败: %v", err)
		}
	}

	if s.clickhouseDB != nil {
		sqlDB, err := s.clickhouseDB.DB()
		if err == nil {
			sqlDB.Close()
		}
	}

	return nil
}

// Sync 执行数据同步
func (s *DataSyncer) Sync(ctx context.Context) error {
	log.Printf("开始同步数据...")

	// 创建ClickHouse表（如果不存在）
	if err := s.createClickHouseTable(); err != nil {
		return fmt.Errorf("failed to create ClickHouse table: %w", err)
	}

	// 获取MongoDB集合
	collection := s.mongoClient.Database(s.config.MongoDatabase).Collection(s.config.MongoCollection)

	// 统计总数
	totalCount, err := collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("failed to count documents: %w", err)
	}

	log.Printf("MongoDB中共有 %d 条记录", totalCount)

	// 分批处理
	var processedCount int64
	var insertedCount int64
	var errorCount int64

	cursor, err := collection.Find(ctx, bson.M{}, options.Find().SetBatchSize(int32(s.config.BatchSize)))
	if err != nil {
		return fmt.Errorf("failed to create cursor: %w", err)
	}
	defer cursor.Close(ctx)

	batch := make([]*SdkIpinfo, 0, s.config.BatchSize)

	for cursor.Next(ctx) {
		var mongoRecord MongoIPInfo
		if err := cursor.Decode(&mongoRecord); err != nil {
			log.Printf("解码记录失败: %v", err)
			errorCount++
			continue
		}

		// 转换为ClickHouse格式
		clickhouseRecord := &SdkIpinfo{
			IP:          mongoRecord.IP,
			Country:     mongoRecord.Country,
			CountryCode: mongoRecord.CountryCode,
			Region:      mongoRecord.Region,
			RegionName:  mongoRecord.RegionName,
			City:        mongoRecord.City,
			District:    mongoRecord.District,
			Zip:         mongoRecord.Zip,
			Lat:         mongoRecord.Lat,
			Lon:         mongoRecord.Lon,
			Timezone:    mongoRecord.Timezone,
			Offset:      mongoRecord.Offset,
			Currency:    mongoRecord.Currency,
			Proxy:       mongoRecord.Proxy,
			Hosting:     mongoRecord.Hosting,
			Asname:      mongoRecord.Asname,
			As:          mongoRecord.As,
			ISP:         mongoRecord.ISP,
			Mobile:      mongoRecord.Mobile,
			CreatedAt:   mongoRecord.CreatedAt,
			UpdatedAt:   mongoRecord.UpdatedAt,
		}

		batch = append(batch, clickhouseRecord)
		processedCount++

		// 批量插入
		if len(batch) >= s.config.BatchSize {
			inserted, err := s.insertBatch(ctx, batch)
			if err != nil {
				log.Printf("批量插入失败: %v", err)
				errorCount += int64(len(batch))
			} else {
				insertedCount += int64(inserted)
			}

			batch = batch[:0] // 清空批次

			// 进度报告
			progress := float64(processedCount) / float64(totalCount) * 100
			log.Printf("进度: %.2f%% (%d/%d), 已插入: %d, 错误: %d",
				progress, processedCount, totalCount, insertedCount, errorCount)
		}
	}

	// 处理剩余的记录
	if len(batch) > 0 {
		inserted, err := s.insertBatch(ctx, batch)
		if err != nil {
			log.Printf("最后批次插入失败: %v", err)
			errorCount += int64(len(batch))
		} else {
			insertedCount += int64(inserted)
		}
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("cursor error: %w", err)
	}

	log.Printf("同步完成! 处理: %d, 插入: %d, 错误: %d", processedCount, insertedCount, errorCount)
	return nil
}

// createClickHouseTable 创建ClickHouse表
func (s *DataSyncer) createClickHouseTable() error {
	if s.config.DryRun {
		log.Println("DRY RUN: 跳过创建表")
		return nil
	}

	// 使用GORM自动迁移
	if err := s.clickhouseDB.AutoMigrate(&SdkIpinfo{}); err != nil {
		return err
	}

	log.Println("ClickHouse表创建/检查完成")
	return nil
}

// insertBatch 批量插入数据
func (s *DataSyncer) insertBatch(ctx context.Context, records []*SdkIpinfo) (int, error) {
	if s.config.DryRun {
		log.Printf("DRY RUN: 跳过插入 %d 条记录", len(records))
		return len(records), nil
	}

	// 使用GORM批量插入，处理重复数据
	result := s.clickhouseDB.WithContext(ctx).Clauses().CreateInBatches(records, len(records))
	if result.Error != nil {
		return 0, result.Error
	}

	return int(result.RowsAffected), nil
}

func main() {
	var (
		mongoURI        = flag.String("mongo-uri", "mongodb://localhost:27017", "MongoDB连接URI")
		mongoDatabase   = flag.String("mongo-db", "sh_proxy", "MongoDB数据库名")
		mongoCollection = flag.String("mongo-collection", "ipinfo", "MongoDB集合名")
		clickhouseDSN   = flag.String("clickhouse-dsn", "tcp://localhost:9000/default", "ClickHouse连接DSN")
		batchSize       = flag.Int("batch-size", 1000, "批量处理大小")
		dryRun          = flag.Bool("dry-run", false, "干运行模式，不实际插入数据")
	)
	flag.Parse()

	config := &SyncConfig{
		MongoURI:        *mongoURI,
		MongoDatabase:   *mongoDatabase,
		MongoCollection: *mongoCollection,
		ClickHouseDSN:   *clickhouseDSN,
		BatchSize:       *batchSize,
		DryRun:          *dryRun,
	}

	log.Printf("同步配置:")
	log.Printf("  MongoDB URI: %s", config.MongoURI)
	log.Printf("  MongoDB Database: %s", config.MongoDatabase)
	log.Printf("  MongoDB Collection: %s", config.MongoCollection)
	log.Printf("  ClickHouse DSN: %s", config.ClickHouseDSN)
	log.Printf("  Batch Size: %d", config.BatchSize)
	log.Printf("  Dry Run: %v", config.DryRun)

	syncer, err := NewDataSyncer(config)
	if err != nil {
		log.Fatalf("创建同步器失败: %v", err)
	}
	defer syncer.Close()

	ctx := context.Background()
	if err := syncer.Sync(ctx); err != nil {
		log.Fatalf("同步失败: %v", err)
	}

	log.Println("同步任务完成!")
}

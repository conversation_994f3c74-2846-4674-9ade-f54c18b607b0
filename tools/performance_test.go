package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"os"
	"sync"
	"sync/atomic"
	"time"

	"github.com/redis/go-redis/v9"
)

// TestConfig 测试配置
type TestConfig struct {
	RedisAddr       string
	ConcurrentConns int
	TestDuration    time.Duration
	IPCount         int
	ConnPerIP       int
}

// PerformanceTest 性能测试器
type PerformanceTest struct {
	rdb    *redis.Client
	config *TestConfig
	
	// 统计指标
	addOps    int64
	removeOps int64
	queryOps  int64
	errors    int64
	
	// 延迟统计
	addLatencies    []time.Duration
	removeLatencies []time.Duration
	queryLatencies  []time.Duration
	latencyMutex    sync.Mutex
}

// NewPerformanceTest 创建性能测试器
func NewPerformanceTest(config *TestConfig) *PerformanceTest {
	rdb := redis.NewClient(&redis.Options{
		Addr:         config.RedisAddr,
		PoolSize:     100,
		MinIdleConns: 10,
	})
	
	return &PerformanceTest{
		rdb:    rdb,
		config: config,
	}
}

// RunTest 运行性能测试
func (pt *PerformanceTest) RunTest(ctx context.Context, useHashFormat bool) error {
	log.Printf("Starting performance test (Hash format: %v)", useHashFormat)
	log.Printf("Config: concurrent=%d, duration=%v, ips=%d, conn_per_ip=%d",
		pt.config.ConcurrentConns, pt.config.TestDuration, pt.config.IPCount, pt.config.ConnPerIP)
	
	// 重置统计
	atomic.StoreInt64(&pt.addOps, 0)
	atomic.StoreInt64(&pt.removeOps, 0)
	atomic.StoreInt64(&pt.queryOps, 0)
	atomic.StoreInt64(&pt.errors, 0)
	
	pt.latencyMutex.Lock()
	pt.addLatencies = make([]time.Duration, 0)
	pt.removeLatencies = make([]time.Duration, 0)
	pt.queryLatencies = make([]time.Duration, 0)
	pt.latencyMutex.Unlock()
	
	// 创建测试上下文
	testCtx, cancel := context.WithTimeout(ctx, pt.config.TestDuration)
	defer cancel()
	
	// 启动并发测试
	var wg sync.WaitGroup
	for i := 0; i < pt.config.ConcurrentConns; i++ {
		wg.Add(1)
		go func(workerID int) {
			defer wg.Done()
			pt.worker(testCtx, workerID, useHashFormat)
		}(i)
	}
	
	// 启动统计协程
	go pt.statsReporter(testCtx)
	
	// 等待测试完成
	wg.Wait()
	
	// 输出最终结果
	pt.printFinalStats()
	
	return nil
}

// worker 工作协程
func (pt *PerformanceTest) worker(ctx context.Context, workerID int, useHashFormat bool) {
	rand.Seed(time.Now().UnixNano() + int64(workerID))
	
	for {
		select {
		case <-ctx.Done():
			return
		default:
			// 随机选择操作类型
			switch rand.Intn(10) {
			case 0, 1, 2, 3, 4: // 50% 添加连接
				pt.testAddConnection(ctx, useHashFormat)
			case 5, 6, 7: // 30% 删除连接
				pt.testRemoveConnection(ctx, useHashFormat)
			case 8, 9: // 20% 查询设备
				pt.testQueryDevice(ctx, useHashFormat)
			}
			
			// 短暂休息
			time.Sleep(time.Millisecond * time.Duration(rand.Intn(10)))
		}
	}
}

// testAddConnection 测试添加连接
func (pt *PerformanceTest) testAddConnection(ctx context.Context, useHashFormat bool) {
	start := time.Now()
	
	// 生成随机数据
	supplier := "supplier1"
	ip := fmt.Sprintf("192.168.1.%d", rand.Intn(pt.config.IPCount)+1)
	sdkId := fmt.Sprintf("sdk%d", rand.Intn(5)+1)
	connId := fmt.Sprintf("conn%d", rand.Intn(1000))
	ctlConnId := fmt.Sprintf("ctl%d", rand.Intn(1000))
	
	var err error
	if useHashFormat {
		err = pt.addConnectionHash(ctx, supplier, ip, sdkId, connId, ctlConnId)
	} else {
		err = pt.addConnectionJSON(ctx, supplier, ip, sdkId, connId, ctlConnId)
	}
	
	duration := time.Since(start)
	
	if err != nil {
		atomic.AddInt64(&pt.errors, 1)
	} else {
		atomic.AddInt64(&pt.addOps, 1)
		pt.recordLatency(&pt.addLatencies, duration)
	}
}

// testRemoveConnection 测试删除连接
func (pt *PerformanceTest) testRemoveConnection(ctx context.Context, useHashFormat bool) {
	start := time.Now()
	
	// 生成随机数据
	supplier := "supplier1"
	ip := fmt.Sprintf("192.168.1.%d", rand.Intn(pt.config.IPCount)+1)
	sdkId := fmt.Sprintf("sdk%d", rand.Intn(5)+1)
	connId := fmt.Sprintf("conn%d", rand.Intn(1000))
	
	var err error
	if useHashFormat {
		err = pt.removeConnectionHash(ctx, supplier, ip, sdkId, connId)
	} else {
		err = pt.removeConnectionJSON(ctx, supplier, ip, sdkId, connId)
	}
	
	duration := time.Since(start)
	
	if err != nil {
		atomic.AddInt64(&pt.errors, 1)
	} else {
		atomic.AddInt64(&pt.removeOps, 1)
		pt.recordLatency(&pt.removeLatencies, duration)
	}
}

// testQueryDevice 测试查询设备
func (pt *PerformanceTest) testQueryDevice(ctx context.Context, useHashFormat bool) {
	start := time.Now()
	
	// 生成随机数据
	supplier := "supplier1"
	ip := fmt.Sprintf("192.168.1.%d", rand.Intn(pt.config.IPCount)+1)
	
	var err error
	if useHashFormat {
		_, err = pt.queryDeviceHash(ctx, supplier, ip)
	} else {
		_, err = pt.queryDeviceJSON(ctx, supplier, ip)
	}
	
	duration := time.Since(start)
	
	if err != nil {
		atomic.AddInt64(&pt.errors, 1)
	} else {
		atomic.AddInt64(&pt.queryOps, 1)
		pt.recordLatency(&pt.queryLatencies, duration)
	}
}

// addConnectionHash Hash格式添加连接
func (pt *PerformanceTest) addConnectionHash(ctx context.Context, supplier, ip, sdkId, connId, ctlConnId string) error {
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, ip)
	fieldKey := fmt.Sprintf("%s:%s:%s", ctlConnId, sdkId, connId)
	
	connData := map[string]interface{}{
		"sdkId":     sdkId,
		"host":      "test-host",
		"connId":    connId,
		"ctlConnId": ctlConnId,
		"timestamp": time.Now().Unix(),
	}
	
	connJSON, _ := json.Marshal(connData)
	return pt.rdb.HSet(ctx, deviceKey, fieldKey, connJSON).Err()
}

// removeConnectionHash Hash格式删除连接
func (pt *PerformanceTest) removeConnectionHash(ctx context.Context, supplier, ip, sdkId, connId string) error {
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, ip)
	
	// 简化版本：直接尝试删除可能的字段
	pattern := fmt.Sprintf("*:%s:%s", sdkId, connId)
	
	// 获取所有字段，找到匹配的
	fields, err := pt.rdb.HKeys(ctx, deviceKey).Result()
	if err != nil {
		return err
	}
	
	for _, field := range fields {
		if len(field) > len(pattern)-1 && field[len(field)-len(pattern)+1:] == pattern[1:] {
			return pt.rdb.HDel(ctx, deviceKey, field).Err()
		}
	}
	
	return nil
}

// queryDeviceHash Hash格式查询设备
func (pt *PerformanceTest) queryDeviceHash(ctx context.Context, supplier, ip string) (map[string]string, error) {
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, ip)
	return pt.rdb.HGetAll(ctx, deviceKey).Result()
}

// addConnectionJSON JSON格式添加连接（模拟锁竞争）
func (pt *PerformanceTest) addConnectionJSON(ctx context.Context, supplier, ip, sdkId, connId, ctlConnId string) error {
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, ip)
	lockKey := fmt.Sprintf("lock:%s", deviceKey)
	
	// 模拟分布式锁
	locked, err := pt.rdb.SetNX(ctx, lockKey, "1", 30*time.Second).Result()
	if err != nil {
		return err
	}
	if !locked {
		return fmt.Errorf("failed to acquire lock")
	}
	defer pt.rdb.Del(ctx, lockKey)
	
	// 模拟读取、修改、写入过程
	time.Sleep(time.Millisecond * time.Duration(rand.Intn(5)+1))
	
	// 简化的JSON更新
	deviceData := map[string]interface{}{
		"sdkIp":    ip,
		"supplier": supplier,
		"location": "US_CA",
		"lastSeen": time.Now().Unix(),
	}
	
	data, _ := json.Marshal(deviceData)
	return pt.rdb.Set(ctx, deviceKey, data, 0).Err()
}

// removeConnectionJSON JSON格式删除连接
func (pt *PerformanceTest) removeConnectionJSON(ctx context.Context, supplier, ip, sdkId, connId string) error {
	// 类似addConnectionJSON，需要锁
	return pt.addConnectionJSON(ctx, supplier, ip, sdkId, connId, "ctl_remove")
}

// queryDeviceJSON JSON格式查询设备
func (pt *PerformanceTest) queryDeviceJSON(ctx context.Context, supplier, ip string) (string, error) {
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, ip)
	return pt.rdb.Get(ctx, deviceKey).Result()
}

// recordLatency 记录延迟
func (pt *PerformanceTest) recordLatency(latencies *[]time.Duration, duration time.Duration) {
	pt.latencyMutex.Lock()
	defer pt.latencyMutex.Unlock()
	*latencies = append(*latencies, duration)
}

// statsReporter 统计报告器
func (pt *PerformanceTest) statsReporter(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			addOps := atomic.LoadInt64(&pt.addOps)
			removeOps := atomic.LoadInt64(&pt.removeOps)
			queryOps := atomic.LoadInt64(&pt.queryOps)
			errors := atomic.LoadInt64(&pt.errors)
			
			log.Printf("Stats: add=%d, remove=%d, query=%d, errors=%d",
				addOps, removeOps, queryOps, errors)
		}
	}
}

// printFinalStats 打印最终统计
func (pt *PerformanceTest) printFinalStats() {
	addOps := atomic.LoadInt64(&pt.addOps)
	removeOps := atomic.LoadInt64(&pt.removeOps)
	queryOps := atomic.LoadInt64(&pt.queryOps)
	errors := atomic.LoadInt64(&pt.errors)
	
	fmt.Printf("\n=== Final Statistics ===\n")
	fmt.Printf("Add operations: %d\n", addOps)
	fmt.Printf("Remove operations: %d\n", removeOps)
	fmt.Printf("Query operations: %d\n", queryOps)
	fmt.Printf("Errors: %d\n", errors)
	fmt.Printf("Total operations: %d\n", addOps+removeOps+queryOps)
	
	// 计算延迟统计
	pt.latencyMutex.Lock()
	defer pt.latencyMutex.Unlock()
	
	if len(pt.addLatencies) > 0 {
		fmt.Printf("Add latency - avg: %v, p95: %v, p99: %v\n",
			pt.calculateAvgLatency(pt.addLatencies),
			pt.calculatePercentile(pt.addLatencies, 95),
			pt.calculatePercentile(pt.addLatencies, 99))
	}
	
	if len(pt.removeLatencies) > 0 {
		fmt.Printf("Remove latency - avg: %v, p95: %v, p99: %v\n",
			pt.calculateAvgLatency(pt.removeLatencies),
			pt.calculatePercentile(pt.removeLatencies, 95),
			pt.calculatePercentile(pt.removeLatencies, 99))
	}
	
	if len(pt.queryLatencies) > 0 {
		fmt.Printf("Query latency - avg: %v, p95: %v, p99: %v\n",
			pt.calculateAvgLatency(pt.queryLatencies),
			pt.calculatePercentile(pt.queryLatencies, 95),
			pt.calculatePercentile(pt.queryLatencies, 99))
	}
}

// calculateAvgLatency 计算平均延迟
func (pt *PerformanceTest) calculateAvgLatency(latencies []time.Duration) time.Duration {
	if len(latencies) == 0 {
		return 0
	}
	
	var total time.Duration
	for _, lat := range latencies {
		total += lat
	}
	return total / time.Duration(len(latencies))
}

// calculatePercentile 计算百分位延迟
func (pt *PerformanceTest) calculatePercentile(latencies []time.Duration, percentile int) time.Duration {
	if len(latencies) == 0 {
		return 0
	}
	
	// 简单排序
	sorted := make([]time.Duration, len(latencies))
	copy(sorted, latencies)
	
	// 冒泡排序（简单实现）
	for i := 0; i < len(sorted); i++ {
		for j := 0; j < len(sorted)-1-i; j++ {
			if sorted[j] > sorted[j+1] {
				sorted[j], sorted[j+1] = sorted[j+1], sorted[j]
			}
		}
	}
	
	index := (len(sorted) * percentile) / 100
	if index >= len(sorted) {
		index = len(sorted) - 1
	}
	
	return sorted[index]
}

func main() {
	if len(os.Args) < 2 {
		log.Fatal("Usage: go run performance_test.go <redis_addr>")
	}
	
	config := &TestConfig{
		RedisAddr:       os.Args[1],
		ConcurrentConns: 50,
		TestDuration:    30 * time.Second,
		IPCount:         100,
		ConnPerIP:       10,
	}
	
	pt := NewPerformanceTest(config)
	ctx := context.Background()
	
	// 测试JSON格式
	fmt.Println("Testing JSON format...")
	if err := pt.RunTest(ctx, false); err != nil {
		log.Fatalf("JSON test failed: %v", err)
	}
	
	// 等待一段时间
	time.Sleep(5 * time.Second)
	
	// 测试Hash格式
	fmt.Println("\nTesting Hash format...")
	if err := pt.RunTest(ctx, true); err != nil {
		log.Fatalf("Hash test failed: %v", err)
	}
}

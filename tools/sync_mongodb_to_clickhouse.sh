#!/bin/bash

# MongoDB到ClickHouse数据同步脚本
# 使用方法: ./sync_mongodb_to_clickhouse.sh [options]

set -e

# 默认配置
MONGO_URI="mongodb://localhost:27017"
MONGO_DB="sh_proxy"
MONGO_COLLECTION="ipinfo"
CLICKHOUSE_DSN="tcp://localhost:9000/default"
BATCH_SIZE=1000
DRY_RUN=false
LOG_FILE="sync_$(date +%Y%m%d_%H%M%S).log"

# 显示帮助信息
show_help() {
    cat << EOF
MongoDB到ClickHouse数据同步脚本

使用方法:
    $0 [选项]

选项:
    -h, --help              显示此帮助信息
    --mongo-uri URI         MongoDB连接URI (默认: $MONGO_URI)
    --mongo-db DB           MongoDB数据库名 (默认: $MONGO_DB)
    --mongo-collection COLL MongoDB集合名 (默认: $MONGO_COLLECTION)
    --clickhouse-dsn DSN    ClickHouse连接DSN (默认: $CLICKHOUSE_DSN)
    --batch-size SIZE       批量处理大小 (默认: $BATCH_SIZE)
    --dry-run               干运行模式，不实际插入数据
    --log-file FILE         日志文件路径 (默认: $LOG_FILE)

示例:
    # 基本同步
    $0

    # 自定义配置同步
    $0 --mongo-uri "***********************************" \\
       --clickhouse-dsn "tcp://user:pass@localhost:9000/mydb" \\
       --batch-size 5000

    # 干运行模式
    $0 --dry-run

    # 生产环境同步
    $0 --mongo-uri "mongodb://prod-mongo:27017" \\
       --clickhouse-dsn "tcp://prod-clickhouse:9000/production" \\
       --batch-size 10000 \\
       --log-file "/var/log/sync_production.log"

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --mongo-uri)
            MONGO_URI="$2"
            shift 2
            ;;
        --mongo-db)
            MONGO_DB="$2"
            shift 2
            ;;
        --mongo-collection)
            MONGO_COLLECTION="$2"
            shift 2
            ;;
        --clickhouse-dsn)
            CLICKHOUSE_DSN="$2"
            shift 2
            ;;
        --batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --dry-run)
            DRY_RUN=true
            shift
            ;;
        --log-file)
            LOG_FILE="$2"
            shift 2
            ;;
        *)
            echo "未知选项: $1"
            echo "使用 --help 查看帮助信息"
            exit 1
            ;;
    esac
done

# 检查依赖
check_dependencies() {
    echo "检查依赖..."
    
    if ! command -v go &> /dev/null; then
        echo "错误: 未找到 Go 编译器"
        exit 1
    fi
    
    echo "依赖检查通过"
}

# 编译同步程序
build_sync_tool() {
    echo "编译同步工具..."
    
    cd "$(dirname "$0")"
    
    if [ ! -f "sync_mongodb_to_clickhouse.go" ]; then
        echo "错误: 未找到 sync_mongodb_to_clickhouse.go 文件"
        exit 1
    fi
    
    # 初始化 Go 模块（如果需要）
    if [ ! -f "go.mod" ]; then
        go mod init sync_tool
        go mod tidy
    fi
    
    # 编译
    go build -o sync_mongodb_to_clickhouse sync_mongodb_to_clickhouse.go
    
    if [ $? -ne 0 ]; then
        echo "错误: 编译失败"
        exit 1
    fi
    
    echo "编译完成"
}

# 测试连接
test_connections() {
    echo "测试数据库连接..."
    
    # 这里可以添加连接测试逻辑
    # 例如使用 mongo 和 clickhouse-client 命令测试连接
    
    echo "连接测试完成"
}

# 执行同步
run_sync() {
    echo "开始数据同步..."
    echo "配置信息:"
    echo "  MongoDB URI: $MONGO_URI"
    echo "  MongoDB Database: $MONGO_DB"
    echo "  MongoDB Collection: $MONGO_COLLECTION"
    echo "  ClickHouse DSN: $CLICKHOUSE_DSN"
    echo "  Batch Size: $BATCH_SIZE"
    echo "  Dry Run: $DRY_RUN"
    echo "  Log File: $LOG_FILE"
    echo ""
    
    # 构建命令参数
    SYNC_ARGS=(
        "--mongo-uri" "$MONGO_URI"
        "--mongo-db" "$MONGO_DB"
        "--mongo-collection" "$MONGO_COLLECTION"
        "--clickhouse-dsn" "$CLICKHOUSE_DSN"
        "--batch-size" "$BATCH_SIZE"
    )
    
    if [ "$DRY_RUN" = true ]; then
        SYNC_ARGS+=("--dry-run")
    fi
    
    # 执行同步并记录日志
    echo "执行命令: ./sync_mongodb_to_clickhouse ${SYNC_ARGS[*]}"
    
    if [ "$LOG_FILE" != "" ]; then
        ./sync_mongodb_to_clickhouse "${SYNC_ARGS[@]}" 2>&1 | tee "$LOG_FILE"
    else
        ./sync_mongodb_to_clickhouse "${SYNC_ARGS[@]}"
    fi
    
    SYNC_EXIT_CODE=$?
    
    if [ $SYNC_EXIT_CODE -eq 0 ]; then
        echo ""
        echo "✅ 同步完成!"
        if [ "$LOG_FILE" != "" ]; then
            echo "日志文件: $LOG_FILE"
        fi
    else
        echo ""
        echo "❌ 同步失败，退出码: $SYNC_EXIT_CODE"
        if [ "$LOG_FILE" != "" ]; then
            echo "请检查日志文件: $LOG_FILE"
        fi
        exit $SYNC_EXIT_CODE
    fi
}

# 清理临时文件
cleanup() {
    echo "清理临时文件..."
    if [ -f "sync_mongodb_to_clickhouse" ]; then
        rm -f sync_mongodb_to_clickhouse
    fi
}

# 主函数
main() {
    echo "MongoDB到ClickHouse数据同步工具"
    echo "=================================="
    echo ""
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 执行步骤
    check_dependencies
    build_sync_tool
    test_connections
    run_sync
    
    echo ""
    echo "所有操作完成!"
}

# 执行主函数
main "$@"

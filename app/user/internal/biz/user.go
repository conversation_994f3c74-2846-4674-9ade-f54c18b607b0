package biz

import (
	"context"
	"fmt"
	"math/rand"
	"os"
	"strconv"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"

	v1 "sh_proxy/api/user/v1"
	"sh_proxy/app/user/internal/data"
	contextlog "sh_proxy/app/user/internal/pkg/log"
	"sh_proxy/pkg/types"
)

type UserUsecase struct {
	log *contextlog.Helper

	data         *data.Data
	ipInfoRepo   data.IPInfoRepo
	deviceRepo   data.DeviceRepo
	eventLogRepo data.EventLogRepo
}

func NewUserUsecase(logger log.Logger, data *data.Data, ipInfoRepo data.IPInfoRepo, deviceRepo data.DeviceRepo, eventLogRepo data.EventLogRepo) *UserUsecase {
	return &UserUsecase{
		log:          contextlog.NewHelper(logger, "module", "biz/user"),
		data:         data,
		ipInfoRepo:   ipInfoRepo,
		deviceRepo:   deviceRepo,
		eventLogRepo: eventLogRepo,
	}
}

func (ju *UserUsecase) Route(ctx context.Context, req *v1.RouteRequest) (*v1.RouteReply, error) {
	ju.log.WithContext(ctx).Infow("msg", "Handling route request",
		"location", req.Location,
		"auth_user", req.AuthUser,
		"session", req.Session,
		"host", req.Host,
		"port", req.Port,
	)

	// 1. 检查会话粘性
	if req.Session != "" {
		return nil, errors.New("session sticky mode not implemented")
	}

	// Retry logic
	for i := 0; i < 3; i++ {
		// 2. 根据位置选择设备
		var selectedDevice *data.DeviceInfo
		var err error
		if req.Location == "" {
			// 混播模式：基于权重随机选择一个设备
			selectedDevice, err = ju.getRandomDeviceForMixedMode(ctx, req.Supplier)
		} else {
			// 指定位置模式：精确匹配或模糊匹配
			selectedDevice, err = ju.getDevicesByLocation(ctx, req.Supplier, req.Location)
		}

		if err != nil {
			ju.log.WithContext(ctx).Errorw("msg", "Failed to get device", "attempt", i+1, "error", err)
			return nil, fmt.Errorf("failed to get a device: %w", err)
		}

		selectedSdkID, selectedConnInfo := ju.selectBestConnection(selectedDevice)

		// Proactive check for unavailable CtlConnId
		isUnavailable, err := ju.deviceRepo.IsCtlUnavailable(ctx, selectedConnInfo.CtlConnId)
		if err != nil {
			ju.log.WithContext(ctx).Errorw("msg", "Failed to check if CtlConnId is unavailable", "ctl_conn_id", selectedConnInfo.CtlConnId, "error", err)
			// Decide if we should continue or return an error. For now, we continue but this could be critical.
		}

		if isUnavailable {
			ju.log.WithContext(ctx).Warnw("msg", "Selected connection's CtlConnId is marked as unavailable, removing and retrying...", "ctlConnId", selectedConnInfo.CtlConnId, "deviceIp", selectedDevice.SdkIP, "connId", selectedConnInfo.ConnId)
			// Remove the specific connection
			ju.deviceRepo.RemoveDevice(ctx, selectedDevice.Supplier, selectedSdkID, selectedDevice.SdkIP, selectedConnInfo.ConnId)
			continue // Retry to find another device
		}

		ju.log.WithContext(ctx).Infow("msg", "Selected device and connection",
			"sdk_id", selectedSdkID,
			"sdk_ip", selectedDevice.SdkIP,
			"host", selectedConnInfo.Host,
			"conn_id", selectedConnInfo.ConnId,
			"location", selectedDevice.Location,
		)

		return &v1.RouteReply{
			Host:   selectedConnInfo.Host,
			SkdId:  selectedSdkID,
			SdkIp:  selectedDevice.SdkIP,
			ConnId: selectedConnInfo.ConnId,
		}, nil
	}

	return nil, errors.New("failed to find a healthy device after multiple retries")
}

func (ju *UserUsecase) RouteByIp(ctx context.Context, req *v1.RouteByIpRequest) (*v1.RouteByIpReply, error) {
	device, err := ju.deviceRepo.GetDevice(ctx, req.Supplier, req.Ip)
	if err != nil {
		return nil, err
	}
	if device == nil {
		return nil, errors.Errorf("not exits: supplier: %s ip： %s ", req.Supplier, req.Ip)
	}
	if len(device.SdkConn) == 0 {
		return nil, errors.Errorf("no sdk: supplier: %s ip： %s ", req.Supplier, req.Ip)
	}

	list := make([]*v1.IpList, 0)
	for sdkId, connList := range device.SdkConn {
		if req.SkdId != "" && sdkId == req.SkdId {
			continue
		}
		for _, conn := range connList {
			list = append(list, &v1.IpList{
				Host:   conn.Host,
				SkdId:  sdkId,
				SdkIp:  device.SdkIP,
				ConnId: conn.ConnId,
			})
		}
	}

	return &v1.RouteByIpReply{List: list}, nil
}

// getDevicesByLocation 根据位置获取设备（支持层级查询）
func (ju *UserUsecase) getDevicesByLocation(ctx context.Context, supplier, location string) (*data.DeviceInfo, error) {
	// 使用新的高性能位置查询
	device, err := ju.deviceRepo.GetRandomDeviceByLocationLevel(ctx, supplier, location)
	if err != nil {
		return nil, err
	}

	if device != nil {
		ju.log.WithContext(ctx).Infow("msg", "Found device using location level query",
			"supplier", supplier,
			"location", location,
			"sdk_ip", device.SdkIP,
		)
		return device, nil
	}

	ju.log.WithContext(ctx).Warnw(
		"msg", "No devices found for location",
		"supplier", supplier,
		"location", location,
	)
	return nil, errors.New("no devices found for location")
}

// getRandomDeviceForMixedMode 混播模式：基于权重随机选择一个设备（高效版本）
func (ju *UserUsecase) getRandomDeviceForMixedMode(ctx context.Context, supplier string) (*data.DeviceInfo, error) {
	ju.log.WithContext(ctx).Infow("msg", "Getting device for mixed mode",
		"supplier", supplier,
	)

	device, err := ju.deviceRepo.GetRandomDeviceForMixedMode(ctx, supplier)
	if err != nil {
		return nil, fmt.Errorf("failed to get random device: %w", err)
	}

	ju.log.WithContext(ctx).Infow("msg", "Selected device for mixed mode",
		"supplier", supplier,
		"sdk_ip", device.SdkIP,
		"location", device.Location,
	)

	return device, nil
}

// ctlUnavailable
func (ju *UserUsecase) CtlUnavailable(ctx context.Context, host, ctlConnId string) error {
	// 设置 key
	if err := ju.deviceRepo.SetCtlUnavailable(ctx, ctlConnId, host); err != nil {
		ju.log.WithContext(ctx).Errorw(
			"msg", "Failed to set ctl unavailable",
			"host", host,
			"ctl_conn_id", ctlConnId,
			"err", err,
		)
		return err
	}

	return nil
}

// selectDevice 智能负载均衡选择设备
func (ju *UserUsecase) selectDevice(devices []*data.DeviceInfo) *data.DeviceInfo {
	if len(devices) == 1 {
		return devices[0]
	}

	// 计算每个设备的负载分数（连接数越少分数越高）
	type deviceScore struct {
		device *data.DeviceInfo
		score  float64
		sdkID  string
	}

	var deviceScores []deviceScore

	for _, device := range devices {
		totalConnections := 0
		var selectedSdkID string

		// 计算设备的总连接数，选择连接数最少的 SDK
		minConnections := int(^uint(0) >> 1) // 最大整数
		for sdkID, connInfos := range device.SdkConn {
			connCount := len(connInfos)
			totalConnections += connCount

			if connCount < minConnections {
				minConnections = connCount
				selectedSdkID = sdkID
			}
		}

		if selectedSdkID == "" {
			continue // 没有可用的 SDK
		}

		// 计算负载分数：连接数越少分数越高
		// 同时考虑设备的最后活跃时间
		timeFactor := 1.0
		if time.Since(device.LastSeen) > 5*time.Minute {
			timeFactor = 0.5 // 长时间未活跃的设备降低优先级
		}

		// 分数 = 基础分数 / (连接数 + 1) * 时间因子
		score := 100.0 / float64(totalConnections+1) * timeFactor

		deviceScores = append(deviceScores, deviceScore{
			device: device,
			score:  score,
			sdkID:  selectedSdkID,
		})
	}

	if len(deviceScores) == 0 {
		// 如果没有可用设备，随机选择一个
		rand.Seed(time.Now().UnixNano())
		return devices[rand.Intn(len(devices))]
	}

	// 基于分数进行加权随机选择
	totalScore := 0.0
	for _, ds := range deviceScores {
		totalScore += ds.score
	}

	if totalScore <= 0 {
		// 如果总分数为0，随机选择
		rand.Seed(time.Now().UnixNano())
		return deviceScores[rand.Intn(len(deviceScores))].device
	}

	// 加权随机选择
	rand.Seed(time.Now().UnixNano())
	randomScore := rand.Float64() * totalScore
	currentScore := 0.0

	for _, ds := range deviceScores {
		currentScore += ds.score
		if randomScore <= currentScore {
			return ds.device
		}
	}

	// 兜底：返回第一个设备
	return deviceScores[0].device
}

// selectBestConnection 从设备中选择最佳的 SDK 和连接
func (ju *UserUsecase) selectBestConnection(device *data.DeviceInfo) (string, *data.ConnInfo) {
	if len(device.SdkConn) == 0 {
		return "", nil
	}

	// 选择连接数最少的 SDK
	var bestSdkID string
	var bestConnInfo *data.ConnInfo
	minConnections := int(^uint(0) >> 1) // 最大整数

	for sdkID, connInfos := range device.SdkConn {
		if len(connInfos) > 0 && len(connInfos) < minConnections {
			minConnections = len(connInfos)
			bestSdkID = sdkID
			bestConnInfo = connInfos[0] // 选择第一个连接
		}
	}

	return bestSdkID, bestConnInfo
}

// HandleDisconnect 处理断连事件
func (ju *UserUsecase) HandleDisconnect(ctx context.Context, req *types.UserEvent) error {
	go ju.logDisconnection(req)

	ju.log.WithContext(ctx).Infow("msg", "Handling disconnect event",
		"sdk_id", req.SdkId,
		"sdk_ip", req.SdkIp,
		"conn_id", req.ConnId,
		"supplier", req.Supplier,
	)

	// 从在线设备中移除连接
	if err := ju.deviceRepo.RemoveDevice(ctx, req.Supplier, req.SdkId, req.SdkIp, req.ConnId); err != nil {
		ju.log.WithContext(ctx).Errorw("msg", "Failed to remove device",
			"sdk_id", req.SdkId,
			"sdk_ip", req.SdkIp,
			"conn_id", req.ConnId,
			"error", err,
		)
		return errors.Wrap(err, "failed to remove device")
	}

	return nil
}

// logDisconnection 异步记录断连日志
func (ju *UserUsecase) logDisconnection(req *types.UserEvent) {
	// 注意：这里是异步调用，没有原始的 context，所以使用普通的日志方法
	ju.log.Infow(
		"msg", "Connection log",
		"sdk_id", req.SdkId,
		"sdk_ip", req.SdkIp,
		"conn_id", req.ConnId,
	)
	if os.Getenv("MY_REGION") == "sg" {
		// 写入 mongo
		ju.eventLogRepo.SaveEventLog(context.Background(), userEventToLog(req))
	} else {
		ju.eventLogRepo.PushEventLog(context.Background(), userEventToLog(req))
	}
}

func userEventToLog(event *types.UserEvent) *data.EventLog {
	return &data.EventLog{
		Name:      event.Name,
		SdkId:     event.SdkId,
		SdkIp:     event.SdkIp,
		Host:      event.Host,
		Supplier:  event.Supplier,
		ConnId:    event.ConnId,
		CtlConnId: event.CtlConnId,
		TimeStamp: strconv.FormatInt(event.TimeStamp, 10),
		Meta:      event.Meta,
	}
}

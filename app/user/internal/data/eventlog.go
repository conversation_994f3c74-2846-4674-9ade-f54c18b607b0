package data

import (
	"context"
	"encoding/json"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"go.mongodb.org/mongo-driver/mongo"

	v1 "sh_proxy/api/consumer/v1"
	"sh_proxy/app/user/internal/conf"
	contextlog "sh_proxy/pkg/log"
)

type EventLog struct {
	Name      string    `bson:"name"`
	SdkId     string    `bson:"sdk_id"`
	SdkIp     string    `bson:"sdk_ip"`
	Host      string    `bson:"host"`
	Supplier  string    `bson:"supplier"`
	ConnId    string    `bson:"conn_id"`
	CtlConnId string    `bson:"ctl_conn_id"`
	TimeStamp string    `bson:"timestamp"`
	Meta      string    `bson:"meta"`
	CreatedAt time.Time `bson:"created_at" json:"created_at"`
	UpdatedAt time.Time `bson:"updated_at" json:"updated_at"`
}

func (e *EventLog) MarshalStr() string {
	data, _ := json.Marshal(e)
	return string(data)
}

type EventLogRepo interface {
	SaveEventLog(ctx context.Context, event *EventLog) error
	PushEventLog(ctx context.Context, event *EventLog) error
}

type eventLogRepo struct {
	data *Data
	log  *contextlog.Helper

	mdb *mongo.Database
}

func NewEventLogRepo(data *Data, cfg *conf.Bootstrap, logger log.Logger) EventLogRepo {
	return &eventLogRepo{
		data: data,
		log:  contextlog.NewHelper(logger, "module", "data/eventlog"),
		mdb:  data.mdb.Database(cfg.Data.Mongodb.DbName),
	}
}

func (r *eventLogRepo) SaveEventLog(ctx context.Context, event *EventLog) error {
	event.CreatedAt = time.Now()
	event.UpdatedAt = time.Now()

	collection := r.mdb.Collection("user_event_log")
	_, err := collection.InsertOne(ctx, event)
	if err != nil {
		r.log.WithContext(ctx).Errorw("msg", "failed to save event log",
			"event", event.MarshalStr(),
			"err", err)
		return err
	}

	return nil
}

func (r *eventLogRepo) PushEventLog(ctx context.Context, event *EventLog) error {
	if _, err := r.data.consumerHttpClient.EventSave(ctx, &v1.EventSaveReq{
		Name:      event.Name,
		SdkId:     event.SdkId,
		SdkIp:     event.SdkIp,
		Host:      event.Host,
		Supplier:  event.Supplier,
		ConnId:    event.ConnId,
		Timestamp: event.TimeStamp,
		Meta:      event.Meta,
	}); err != nil {
		r.log.WithContext(ctx).Errorw("msg", "failed to save event log by sg",
			"event", event.MarshalStr(),
			"err", err)
		return err
	}

	return nil
}

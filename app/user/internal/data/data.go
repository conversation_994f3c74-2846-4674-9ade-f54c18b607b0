package data

import (
	"context"

	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	v1 "sh_proxy/api/consumer/v1"
	"sh_proxy/app/user/internal/conf"
	contextlog "sh_proxy/app/user/internal/pkg/log"
	"sh_proxy/pkg/utils"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
	NewData,
	NewMongoClient,
	NewRabbitMQClient,
	NewLocationMappingService,
	NewConsumerHttpClient,

	NewIPInfoRepo,
	NewDeviceRepo,
	NewEventLogRepo,
)

// Data .
type Data struct {
	log *contextlog.Helper

	rdb                *redis.Client
	mdb                *mongo.Client
	MQConn             *MQConn
	consumerHttpClient v1.ConsumerHTTPClient

	lockManager *LockManager
	crLock      *LockManager
}

// NewData .
func NewData(c *conf.Bootstrap, logger log.Logger, mdb *mongo.Client, mqConn *MQConn, chc v1.ConsumerHTTPClient) (*Data, func(), error) {
	l := contextlog.NewHelper(logger, "module", "data/user")

	redisClient, err := NewRedisClient(c.Data.Redis, logger)
	if err != nil {
		return nil, nil, errors.Wrap(err, "new redis client")
	}
	l.Info("redis connected")
	crRedisClient, err := NewRedisClient(c.Data.CrRedis, logger)
	if err != nil {
		return nil, nil, errors.Wrap(err, "new cr redis client")
	}
	l.Info("cr redis connected")
	crLock := NewLockManager(crRedisClient)
	lockManager := NewLockManager(redisClient)

	d := &Data{
		rdb:                redisClient,
		log:                l,
		mdb:                mdb,
		MQConn:             mqConn,
		crLock:             crLock,
		lockManager:        lockManager,
		consumerHttpClient: chc,
	}

	cleanup := func() {
		d.log.Info("closing the data resources")
		d.rdb.Close()
		crRedisClient.Close()
		d.mdb.Disconnect(context.TODO())
	}
	return d, cleanup, nil
}

// NewRedisClient 创建Redis客户端
func NewRedisClient(cfg *conf.Data_Redis, logger log.Logger) (*redis.Client, error) {
	// l := log.NewHelper(log.With(logger, "module", "data/user/redis"))
	rdb := redis.NewClient(&redis.Options{
		Addr:         cfg.Addr,
		Username:     cfg.Username,
		Password:     cfg.Password,
		DB:           int(cfg.Db),
		DialTimeout:  cfg.DialTimeout.AsDuration(),
		WriteTimeout: cfg.WriteTimeout.AsDuration(),
		ReadTimeout:  cfg.ReadTimeout.AsDuration(),
	})
	if rdb == nil {
		return nil, errors.New("failed opening connection to redis")
	}
	// rdb.AddHook(redisotel.NewTracingHook())
	if _, err := rdb.Ping(context.Background()).Result(); err != nil {
		return nil, errors.Wrap(err, "failed to ping redis")
	}
	// l.Info("redis connected")

	return rdb, nil
}

func NewMongoClient(cfg *conf.Bootstrap, logger log.Logger) (*mongo.Client, error) {
	l := log.NewHelper(log.With(logger, "module", "data/user/mongo"))
	// 创建客户端
	ctx, cancel := context.WithTimeout(context.Background(), cfg.Data.Mongodb.Timeout.AsDuration())
	defer cancel()

	clientOptions := options.Client().ApplyURI(cfg.Data.Mongodb.Uri)
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		l.Errorf("failed to connect to mongodb, %v", err)
		return nil, err
	}

	if utils.IsK8sEnv() {
		// 验证连接
		if err = client.Ping(ctx, nil); err != nil {
			l.Errorf("failed to ping mongodb, %v", err)
			return nil, err
		}
	}

	l.Info("mongodb connected")

	return client, nil
}

func NewConsumerHttpClient(cfg *conf.Bootstrap) (v1.ConsumerHTTPClient, error) {
	client, err := http.NewClient(
		context.Background(),
		http.WithEndpoint(cfg.SgConsumer.GetDomain()),
	)
	if err != nil {
		return nil, errors.Wrap(err, "new consumer http client")
	}

	return v1.NewConsumerHTTPClient(client), nil
}

func (d *Data) PublishMsg(ctx context.Context, body []byte) error {
	return d.MQConn.PublishMsg(ctx, "", d.MQConn.queue, body)
}

package data

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"sh_proxy/pkg/types"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
)

type (
	ConnInfo types.ConnInfo
)

// DeviceInfo 设备信息
type DeviceInfo struct {
	SdkIP     string                 `json:"sdkIp"`
	Supplier  string                 `json:"supplier"`
	Location  string                 `json:"location"`
	SdkConn   map[string][]*ConnInfo `json:"sdkConn"` // key 为 sdkId, value 为连接信息
	LastSeen  time.Time              `json:"lastSeen"`
	CreatedAt time.Time              `json:"createdAt"`
}

// GetDeviceKey 获取设备的Redis key
func (d *DeviceInfo) GetDeviceKey() string {
	return fmt.Sprintf("device:%s:%s", d.Supplier, d.SdkIP)
}

// GetLocationKey 获取位置的Redis key
func GetLocationKey(supplier, location string) string {
	return fmt.Sprintf("location:%s:%s", supplier, location)
}

// GetSessionKey 获取会话的Redis key
func GetSessionKey(authUser, session string) string {
	return fmt.Sprintf("session:%s:%s", authUser, session)
}

// LocationStats 位置统计信息
// type LocationStats struct {
// 	Supplier    string `json:"supplier"`
// 	Location    string `json:"location"`
// 	DeviceCount int64  `json:"deviceCount"`
// 	Weight      int64  `json:"weight"`
// }

// WeightedLocation 加权位置信息
type WeightedLocation struct {
	Supplier string `json:"supplier"`
	Location string `json:"location"`
	Weight   int64  `json:"weight"`
}

// DeviceRepo 设备仓库接口
type DeviceRepo interface {
	// 连接管理
	AddDevice(ctx context.Context, device *DeviceInfo, sdkID string, connInfo *ConnInfo) error
	RemoveDevice(ctx context.Context, supplier, sdkID, sdkIP, connID string) error
	GetDevice(ctx context.Context, supplier, sdkIP string) (*DeviceInfo, error)

	// 位置查询
	GetDevicesByLocation(ctx context.Context, supplier, location string) ([]*DeviceInfo, error)
	GetRandomDeviceForMixedMode(ctx context.Context, supplier string) (*DeviceInfo, error)
	GetRandomDeviceByLocationLevel(ctx context.Context, supplier, location string) (*DeviceInfo, error)

	// 位置统计和管理
	GetLocationDeviceCount(ctx context.Context, supplier, location string) (int64, error)
	UpdateLocationStats(ctx context.Context, supplier, location string, deviceCount int64) error

	// 混播模式权重管理
	UpdateLocationWeight(ctx context.Context, supplier, location string, weight int64) error
	GetRandomLocationByWeight(ctx context.Context, supplier string) (string, error)

	// 会话管理
	SetSessionDevice(ctx context.Context, authUser, session, sdkID, sdkIP string, ttl time.Duration) error
	GetSessionDevice(ctx context.Context, authUser, session string) (string, string, error)

	// 中控设置为 unavailable
	SetCtlUnavailable(ctx context.Context, ctlConnId, host string) error
	IsCtlUnavailable(ctx context.Context, ctlConnId string) (bool, error)
}

type deviceRepo struct {
	data          *Data
	lockManager   *LockManager
	locationQuery *LocationQueryStrategy
	lms           *LocationMappingService
}

// NewDeviceRepo 创建设备仓库
func NewDeviceRepo(data *Data, lms *LocationMappingService) DeviceRepo {
	return &deviceRepo{
		data:          data,
		lockManager:   data.lockManager,
		locationQuery: NewLocationQueryStrategy(data.rdb),
		lms:           lms,
	}
}

// AddDevice 添加设备连接（使用分布式锁）
func (r *deviceRepo) AddDevice(ctx context.Context, device *DeviceInfo, sdkID string, connInfo *ConnInfo) error {
	lockKey := GetDeviceLockKey(device.Supplier, device.SdkIP)
	lockTTL := 30 * time.Second

	// 使用分布式锁确保并发安全
	return r.lockManager.WithLockRetry(ctx, lockKey, lockTTL, 3, 100*time.Millisecond, func() error {
		return r.addDeviceWithoutLock(ctx, device, sdkID, connInfo)
	})
}

// addDeviceWithoutLock 不使用锁的添加设备方法（内部使用）
func (r *deviceRepo) addDeviceWithoutLock(ctx context.Context, device *DeviceInfo, sdkID string, connInfo *ConnInfo) error {
	deviceKey := device.GetDeviceKey()
	locationKey := GetLocationKey(device.Supplier, device.Location)

	// 获取现有设备信息
	existingDevice, _ := r.GetDevice(ctx, device.Supplier, device.SdkIP)
	if existingDevice != nil {
		device.SdkConn = existingDevice.SdkConn
		device.CreatedAt = existingDevice.CreatedAt
	} else {
		device.CreatedAt = time.Now()
	}
	// 不存在连接信息，则初始化
	if device.SdkConn == nil {
		device.SdkConn = make(map[string][]*ConnInfo)
	}

	// connInfo.Timestamp = time.Now().Format(time.RFC3339)
	// 检查连接是否已存在
	if _, exists := device.SdkConn[sdkID]; exists {
		for _, conn := range device.SdkConn[sdkID] {
			if conn.ConnId == connInfo.ConnId {
				r.data.log.Warnf("Connection ID already exists: %s:%s, connID: %s",
					device.SdkIP, sdkID, connInfo.ConnId)
				return nil // 连接已存在，不需要重复添加
			}
		}
		device.SdkConn[sdkID] = append(device.SdkConn[sdkID], connInfo)
	} else {
		device.SdkConn[sdkID] = []*ConnInfo{connInfo}
	}

	device.LastSeen = time.Now()

	// 使用事务确保原子性
	pipe := r.data.rdb.TxPipeline()

	// 序列化设备信息
	deviceData, err := json.Marshal(device)
	if err != nil {
		return fmt.Errorf("failed to marshal device info: %w", err)
	}

	// 存储设备信息
	pipe.Set(ctx, deviceKey, deviceData, redis.KeepTTL)

	// 添加到位置集合
	pipe.SAdd(ctx, locationKey, device.SdkIP)

	// 更新混播权重索引
	r.updateMixedModeWeight(ctx, pipe, device.Supplier, device.Location, 1)

	// 执行事务
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute redis transaction: %w", err)
	}

	r.data.log.Infof("Device connection added: %s:%s, connID: %s, location: %s, total_connections: %d",
		device.SdkIP, sdkID, connInfo.ConnId, device.Location, len(device.SdkConn[sdkID]))

	return nil
}

// RemoveDevice 移除设备连接（使用分布式锁）
func (r *deviceRepo) RemoveDevice(ctx context.Context, supplier, sdkID, sdkIP, connID string) error {
	lockKey := GetDeviceLockKey(supplier, sdkIP)
	lockTTL := 30 * time.Second

	// 使用分布式锁确保并发安全
	return r.lockManager.WithLockRetry(ctx, lockKey, lockTTL, 3, 100*time.Millisecond, func() error {
		return r.removeDeviceWithoutLock(ctx, supplier, sdkID, sdkIP, connID)
	})
}

// removeDeviceWithoutLock 不使用锁的移除设备方法（内部使用）
func (r *deviceRepo) removeDeviceWithoutLock(ctx context.Context, supplier, sdkID, sdkIP, connID string) error {
	// 获取现有设备信息
	device, err := r.GetDevice(ctx, supplier, sdkIP)
	if err != nil || device == nil {
		r.data.log.WithContext(ctx).Warnf("Device not found for removal: %s:%s", sdkID, sdkIP)
		return nil // 设备不存在，认为已经移除
	}

	if device.SdkConn == nil || len(device.SdkConn) == 0 {
		r.data.log.WithContext(ctx).Warnf("Device no sdk for removal: %s:%s", sdkID, sdkIP)
		return nil // 设备不存在，认为已经移除
	}
	if _, exists := device.SdkConn[sdkID]; !exists {
		r.data.log.WithContext(ctx).Warnf("Device sdk not found for removal: %s:%s", sdkID, sdkIP)
		return nil // 设备不存在，认为已经移除
	}

	var (
		existsConns = device.SdkConn[sdkID]
		newConns    []*ConnInfo
	)
	for i, conn := range existsConns {
		if conn.ConnId != connID {
			newConns = append(newConns, existsConns[i])
		}
	}
	if len(existsConns) == len(newConns) {
		r.data.log.WithContext(ctx).Warnf("Device sdk conn not found for removal: %s:%s, connID: %s", sdkID, sdkIP, connID)
		return nil // 设备不存在，认为已经移除
	}

	device.SdkConn[sdkID] = newConns
	device.LastSeen = time.Now()
	if len(device.SdkConn[sdkID]) == 0 {
		delete(device.SdkConn, sdkID)
	}

	if err := r.removeDeviceFromRedis(ctx, supplier, sdkIP, device); err != nil {
		return err
	}
	r.data.log.WithContext(ctx).Infof("Device connection removed: %s:%s, connID: %s, remaining connections: %d",
		sdkID, sdkIP, connID, len(device.SdkConn[sdkID]))

	return nil
}

func (r *deviceRepo) removeDeviceFromRedis(ctx context.Context, supplier, sdkIP string, device *DeviceInfo) error {
	if supplier == "" {
		supplier = device.Supplier
	}
	if sdkIP == "" {
		sdkIP = device.SdkIP
	}
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, sdkIP)

	pipe := r.data.rdb.TxPipeline()

	// 如果还有其他连接，更新设备信息
	if len(device.SdkConn) > 0 {
		deviceData, err := json.Marshal(device)
		if err != nil {
			return fmt.Errorf("failed to marshal device info: %w", err)
		}
		pipe.Set(ctx, deviceKey, deviceData, redis.KeepTTL)
	} else {
		// 没有连接了，完全移除设备
		pipe.Del(ctx, deviceKey)

		// 从位置集合中移除
		locationKey := GetLocationKey(supplier, device.Location)
		pipe.SRem(ctx, locationKey, sdkIP)

		// 更新位置统计信息
		// statsKey := fmt.Sprintf("location_stats:%s:%s", device.Supplier, device.Location)
		// pipe.Decr(ctx, statsKey)

		// 更新混播权重索引
		r.updateMixedModeWeight(ctx, pipe, device.Supplier, device.Location, -1)
		// 更新对应 location 权重
		r.lms.RemoveDeviceLocationMapping(ctx, pipe, device.Supplier, device.Location)
	}

	// 执行事务
	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute redis transaction: %w", err)
	}

	return nil
}

// GetDevice 获取设备信息
func (r *deviceRepo) GetDevice(ctx context.Context, supplier, sdkIP string) (*DeviceInfo, error) {
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, sdkIP)

	data, err := r.data.rdb.Get(ctx, deviceKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, err
	}

	var device DeviceInfo
	if err := json.Unmarshal([]byte(data), &device); err != nil {
		r.data.log.WithContext(ctx).Errorf("Failed to unmarshal device info: %v", err)
		return nil, fmt.Errorf("failed to unmarshal device info: %w", err)
	}

	return &device, nil
}

// GetDevicesByLocation 根据位置获取设备列表
func (r *deviceRepo) GetDevicesByLocation(ctx context.Context, supplier, location string) ([]*DeviceInfo, error) {
	locationKey := GetLocationKey(supplier, location)

	sdkIPs, err := r.data.rdb.SMembers(ctx, locationKey).Result()
	if err != nil {
		return nil, err
	}

	var devices []*DeviceInfo
	for _, sdkIP := range sdkIPs {
		device, err := r.GetDevice(ctx, supplier, sdkIP)
		if err != nil || device == nil {
			// 设备不存在，从位置集合中清理
			r.data.rdb.SRem(ctx, locationKey, sdkIP)
			continue
		}

		// 检查设备是否有活跃连接
		if len(device.SdkConn) == 0 {
			// 没有活跃连接，从位置集合中清理
			r.data.rdb.SRem(ctx, locationKey, sdkIP)
			continue
		}

		devices = append(devices, device)
	}

	return devices, nil
}

// GetRandomDeviceByLocationLevel 根据位置层级随机获取设备（高性能版本）
func (r *deviceRepo) GetRandomDeviceByLocationLevel(ctx context.Context, supplier, location string) (*DeviceInfo, error) {
	// 使用新的位置查询策略
	ip, err := r.locationQuery.GetRandomIPByLocation(ctx, supplier, location)
	if err != nil {
		return nil, err
	}

	if ip == "" {
		return nil, nil
	}

	// 获取设备信息
	device, err := r.GetDevice(ctx, supplier, ip)
	if err != nil || device == nil {
		// 设备不存在，需要清理索引
		r.cleanupDeviceFromLocationIndex(ctx, supplier, location, ip)
		return nil, nil
	}

	// 检查设备是否有活跃连接
	if len(device.SdkConn) == 0 {
		// 没有活跃连接，清理索引
		r.cleanupDeviceFromLocationIndex(ctx, supplier, location, ip)
		return nil, nil
	}

	return device, nil
}

// GetRandomDeviceByLocationLevelDirect 直接从位置索引获取随机设备（最高性能）
func (r *deviceRepo) GetRandomDeviceByLocationLevelDirect(ctx context.Context, supplier, location string) (*DeviceInfo, error) {
	// 使用直接查询策略（不考虑权重，性能最优）
	ip, err := r.locationQuery.GetRandomIPByLocationDirect(ctx, supplier, location)
	if err != nil {
		return nil, err
	}

	if ip == "" {
		return nil, nil
	}

	// 获取设备信息
	device, err := r.GetDevice(ctx, supplier, ip)
	if err != nil || device == nil {
		// 设备不存在，需要清理索引
		r.cleanupDeviceFromLocationIndex(ctx, supplier, location, ip)
		return nil, nil
	}

	// 检查设备是否有活跃连接
	if len(device.SdkConn) == 0 {
		// 没有活跃连接，清理索引
		r.cleanupDeviceFromLocationIndex(ctx, supplier, location, ip)
		return nil, nil
	}

	return device, nil
}

// BatchGetRandomDevicesByLocation 批量获取随机设备
func (r *deviceRepo) BatchGetRandomDevicesByLocation(ctx context.Context, supplier, location string, count int) ([]*DeviceInfo, error) {
	// 批量获取IP
	ips, err := r.locationQuery.BatchGetRandomIPs(ctx, supplier, location, count)
	if err != nil {
		return nil, err
	}

	var devices []*DeviceInfo
	for _, ip := range ips {
		device, err := r.GetDevice(ctx, supplier, ip)
		if err != nil || device == nil {
			// 设备不存在，清理索引
			r.cleanupDeviceFromLocationIndex(ctx, supplier, location, ip)
			continue
		}

		// 检查设备是否有活跃连接
		if len(device.SdkConn) == 0 {
			// 没有活跃连接，清理索引
			r.cleanupDeviceFromLocationIndex(ctx, supplier, location, ip)
			continue
		}

		devices = append(devices, device)
	}

	return devices, nil
}

// GetLocationIPCount 获取位置的IP数量（支持层级查询）
func (r *deviceRepo) GetLocationIPCount(ctx context.Context, supplier, location string) (int64, error) {
	parts := strings.Split(location, "_")

	switch len(parts) {
	case 1:
		// 国家级：统计所有州的权重总和
		countryWeightKey := fmt.Sprintf("location_weight:%s:country:%s", supplier, location)
		return r.data.rdb.ZCard(ctx, countryWeightKey).Result()
	case 2:
		// 州级：统计所有城市的权重总和
		stateWeightKey := fmt.Sprintf("location_weight:%s:state:%s", supplier, location)
		return r.data.rdb.ZCard(ctx, stateWeightKey).Result()
	case 3:
		// 城市级：直接统计IP数量
		locationKey := GetLocationKey(supplier, location)
		return r.data.rdb.SCard(ctx, locationKey).Result()
	default:
		return 0, fmt.Errorf("invalid location format: %s", location)
	}
}

// cleanupDeviceFromLocationIndex 清理设备在位置索引中的记录
func (r *deviceRepo) cleanupDeviceFromLocationIndex(ctx context.Context, supplier, location, ip string) {
	// 异步清理，不影响主流程
	go func() {
		// 从原始位置集合中移除
		locationKey := GetLocationKey(supplier, location)
		r.data.rdb.SRem(ctx, locationKey, ip)

		// 注意：位置映射的清理将在 Consumer 服务中处理
	}()
}

func (r *deviceRepo) GetDeviceByLocation(ctx context.Context, supplier, location string) (*DeviceInfo, error) {
	locationKey := GetLocationKey(supplier, location)

	sdkIP, err := r.data.rdb.SRandMember(ctx, locationKey).Result()
	if err != nil {
		return nil, err
	}

	device, err := r.GetDevice(ctx, supplier, sdkIP)
	if err != nil || device == nil {
		// 设备不存在，从位置集合中清理
		r.data.rdb.SRem(ctx, locationKey, sdkIP)
		return nil, nil
	}

	// 检查设备是否有活跃连接
	if len(device.SdkConn) == 0 {
		// 没有活跃连接，从位置集合中清理
		r.data.rdb.SRem(ctx, locationKey, sdkIP)
		return nil, nil
	}

	return device, nil
}

func (r *deviceRepo) SetCtlUnavailable(ctx context.Context, ctlConnId, host string) error {
	unavailableKey := "ctlunavailable"

	if err := r.data.rdb.SAdd(ctx, unavailableKey, ctlConnId).Err(); err != nil {
		return errors.Wrap(err, "failed to add ctlunavailable")
	}
	return nil
}

// UpdateLocationStats 更新位置统计信息
func (r *deviceRepo) UpdateLocationStats(ctx context.Context, supplier, location string, deviceCount int64) error {
	statsKey := fmt.Sprintf("location_stats:%s:%s", supplier, location)
	return r.data.rdb.Set(ctx, statsKey, deviceCount, 24*time.Hour).Err()
}

// SetSessionDevice 设置会话绑定的设备
func (r *deviceRepo) SetSessionDevice(ctx context.Context, authUser, session, sdkID, sdkIP string, ttl time.Duration) error {
	sessionKey := GetSessionKey(authUser, session)
	deviceIdentifier := fmt.Sprintf("%s:%s", sdkID, sdkIP)

	return r.data.rdb.Set(ctx, sessionKey, deviceIdentifier, ttl).Err()
}

// GetSessionDevice 获取会话绑定的设备
func (r *deviceRepo) GetSessionDevice(ctx context.Context, authUser, session string) (string, string, error) {
	sessionKey := GetSessionKey(authUser, session)

	deviceIdentifier, err := r.data.rdb.Get(ctx, sessionKey).Result()
	if err != nil {
		if err == redis.Nil {
			return "", "", nil
		}
		return "", "", err
	}

	parts := strings.Split(deviceIdentifier, ":")
	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid device identifier: %s", deviceIdentifier)
	}

	return parts[0], parts[1], nil
}

// GetLocationDeviceCount 获取位置的设备数量
func (r *deviceRepo) GetLocationDeviceCount(ctx context.Context, supplier, location string) (int64, error) {
	locationKey := GetLocationKey(supplier, location)
	return r.data.rdb.SCard(ctx, locationKey).Result()
}

// GetRandomDeviceForMixedMode 混播模式：基于权重随机选择一个设备（高效版本）
func (r *deviceRepo) GetRandomDeviceForMixedMode(ctx context.Context, supplier string) (*DeviceInfo, error) {
	// 1. 基于权重随机选择一个位置
	selectedLocation, err := r.GetRandomLocationByWeight(ctx, supplier)
	if err != nil {
		return nil, fmt.Errorf("failed to select random location: %w", err)
	}

	if selectedLocation == "" {
		return nil, fmt.Errorf("no available locations for supplier: %s", supplier)
	}

	// 2. 从选中的位置随机选择一个设备
	selectedDevice, err := r.GetDeviceByLocation(ctx, supplier, selectedLocation)
	if err != nil {
		return nil, fmt.Errorf("failed to get device from location %s: %w", selectedLocation, err)
	}
	if selectedDevice == nil {
		return nil, fmt.Errorf("no devices available in location: %s", selectedLocation)
	}

	// 2. 从选中的位置随机选择一个设备
	// devices, err := r.GetDevicesByLocation(ctx, supplier, selectedLocation)
	// if err != nil {
	// 	return nil, fmt.Errorf("failed to get devices from location %s: %w", selectedLocation, err)
	// }
	//
	// if len(devices) == 0 {
	// 	// 位置没有设备，清理权重索引
	// 	r.cleanupLocationWeight(ctx, supplier, selectedLocation)
	// 	return nil, fmt.Errorf("no devices available in location: %s", selectedLocation)
	// }
	//
	// // 3. 随机选择一个设备
	// rand.Seed(time.Now().UnixNano())
	// selectedDevice := devices[rand.Intn(len(devices))]

	return selectedDevice, nil
}

// GetRandomLocationByWeight 基于权重随机选择位置
func (r *deviceRepo) GetRandomLocationByWeight(ctx context.Context, supplier string) (string, error) {
	// 使用 Redis 的 ZRANDMEMBER 命令基于权重随机选择
	// 权重索引键：mixed_weight:{supplier}
	weightKey := fmt.Sprintf("mixed_weight:%s", supplier)

	// 使用 ZRANDMEMBER 命令随机选择一个位置（基于分数权重）
	result, err := r.data.rdb.ZRandMemberWithScores(ctx, weightKey, 1).Result()
	if err != nil {
		if err == redis.Nil {
			return "", nil // 没有可用位置
		}
		return "", fmt.Errorf("failed to get random weighted location: %w", err)
	}

	if len(result) == 0 {
		return "", nil
	}

	return result[0].Member.(string), nil
}

// UpdateLocationWeight 更新位置权重
func (r *deviceRepo) UpdateLocationWeight(ctx context.Context, supplier, location string, weight int64) error {
	weightKey := fmt.Sprintf("mixed_weight:%s", supplier)

	if weight <= 0 {
		// 权重为0或负数，从权重索引中移除
		return r.data.rdb.ZRem(ctx, weightKey, location).Err()
	}

	// 更新权重
	return r.data.rdb.ZAdd(ctx, weightKey, redis.Z{
		Score:  float64(weight),
		Member: location,
	}).Err()
}

// updateMixedModeWeight 更新混播模式权重（内部方法）
func (r *deviceRepo) updateMixedModeWeight(ctx context.Context, pipe redis.Pipeliner, supplier, location string, delta int64) {
	weightKey := fmt.Sprintf("mixed_weight:%s", supplier)

	if delta > 0 {
		// 增加权重
		pipe.ZIncrBy(ctx, weightKey, float64(delta), location)
		pipe.Expire(ctx, weightKey, 24*time.Hour)
	} else {
		// 减少权重
		pipe.ZIncrBy(ctx, weightKey, float64(delta), location)
		// 如果权重降到0或以下，移除该位置
		pipe.ZRemRangeByScore(ctx, weightKey, "-inf", "0")
	}
}

// cleanupLocationWeight 清理无效的位置权重
func (r *deviceRepo) cleanupLocationWeight(ctx context.Context, supplier, location string) {
	weightKey := fmt.Sprintf("mixed_weight:%s", supplier)
	r.data.rdb.ZRem(ctx, weightKey, location)
}

// IsCtlUnavailable checks if a ctlConnId is marked as unavailable
func (r *deviceRepo) IsCtlUnavailable(ctx context.Context, ctlConnId string) (bool, error) {
	unavailableKey := "ctlunavailable"
	exists, err := r.data.rdb.SIsMember(ctx, unavailableKey, ctlConnId).Result()
	if err != nil {
		return false, errors.Wrap(err, "failed to check ctlunavailable status")
	}
	return exists, nil
}

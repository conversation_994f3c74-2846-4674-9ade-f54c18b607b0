package data

import (
	"context"
	"fmt"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
)

// LocationMappingService 位置映射服务（在Consumer中维护位置映射关系）
type LocationMappingService struct {
	log  *log.Helper
	data *Data
}

func NewLocationMappingService(logger log.Logger, data *Data) *LocationMappingService {
	return &LocationMappingService{
		log:  log.NewHelper(log.With(logger, "module", "service/location_mapping")),
		data: data,
	}
}

// AddDeviceLocationMapping 添加设备位置映射
func (s *LocationMappingService) AddDeviceLocationMapping(ctx context.Context, pipe redis.Pipeliner, supplier, location string) error {
	country, state, city := s.parseLocation(location)

	// 添加国家->州的映射
	if country != "" && state != "" {
		// countryMappingKey := s.getLocationMappingKey(supplier, "country", country)
		countryWeightKey := s.getLocationWeightKey(supplier, "country", country)
		// pipe.SAdd(ctx, countryMappingKey, state)
		pipe.ZIncrBy(ctx, countryWeightKey, 1, state)
		// pipe.Expire(ctx, countryMappingKey, redis.KeepTTL)
		// pipe.Expire(ctx, countryWeightKey, redis.KeepTTL)
	}

	// 添加州->城市的映射
	if country != "" && state != "" && city != "" {
		// stateMappingKey := s.getLocationMappingKey(supplier, "state", state)
		stateWeightKey := s.getLocationWeightKey(supplier, "state", state)
		// pipe.SAdd(ctx, stateMappingKey, city)
		pipe.ZIncrBy(ctx, stateWeightKey, 1, city)
		// pipe.Expire(ctx, stateMappingKey, redis.KeepTTL)
		// pipe.Expire(ctx, stateWeightKey, redis.KeepTTL)
	}

	// _, err := pipe.Exec(ctx)
	// if err != nil {
	// 	s.log.Errorw("添加位置映射失败",
	// 		"supplier", supplier,
	// 		"location", location,
	// 		"ip", ip,
	// 		"error", err,
	// 	)
	// 	return err
	// }
	//
	// s.log.Infow("添加位置映射成功",
	// 	"supplier", supplier,
	// 	"location", location,
	// 	"ip", ip,
	// 	"country", country,
	// 	"state", state,
	// 	"city", city,
	// )

	return nil
}

// RemoveDeviceLocationMapping 移除设备位置映射
func (s *LocationMappingService) RemoveDeviceLocationMapping(ctx context.Context, pipe redis.Pipeliner, supplier, location string) error {
	country, state, city := s.parseLocation(location)

	// pipe := s.data.rdb.TxPipeline()

	// 减少国家->州的权重
	if country != "" && state != "" {
		countryWeightKey := s.getLocationWeightKey(supplier, "country", country)
		pipe.ZIncrBy(ctx, countryWeightKey, -1, state)
		pipe.ZRemRangeByScore(ctx, countryWeightKey, "-inf", "0") // 清理零权重
	}

	// 减少州->城市的权重
	if country != "" && state != "" && city != "" {
		stateWeightKey := s.getLocationWeightKey(supplier, "state", state)
		pipe.ZIncrBy(ctx, stateWeightKey, -1, city)
		pipe.ZRemRangeByScore(ctx, stateWeightKey, "-inf", "0") // 清理零权重
	}

	// _, err := pipe.Exec(ctx)
	// if err != nil {
	// 	s.log.Errorw("移除位置映射失败",
	// 		"supplier", supplier,
	// 		"location", location,
	// 		"ip", ip,
	// 		"error", err,
	// 	)
	// 	return err
	// }

	// 异步清理空的映射集合
	go s.cleanupEmptyMappings(ctx, supplier, country, state, city)

	// s.log.Infow("移除位置映射成功",
	// 	"supplier", supplier,
	// 	"location", location,
	// 	"ip", ip,
	// )

	return nil
}

// cleanupEmptyMappings 清理空的映射集合
func (s *LocationMappingService) cleanupEmptyMappings(ctx context.Context, supplier, country, state, city string) {
	// 检查并清理州映射
	if country != "" && state != "" && city != "" {
		stateWeightKey := s.getLocationWeightKey(supplier, "state", strings.Join([]string{country, state}, ":"))
		count, err := s.data.rdb.ZCard(ctx, stateWeightKey).Result()
		if err == nil && count == 0 {
			// stateMappingKey := s.getLocationMappingKey(supplier, "state", state)
			// s.data.rdb.Del(ctx, stateMappingKey)
			s.data.rdb.Del(ctx, stateWeightKey)

			// 从国家映射中移除这个州
			// if country != "" {
			// 	countryMappingKey := s.getLocationMappingKey(supplier, "country", country)
			// 	s.data.rdb.SRem(ctx, countryMappingKey, state)
			// }
		}
	}

	// 检查并清理国家映射
	if country != "" {
		countryWeightKey := s.getLocationWeightKey(supplier, "country", country)
		count, err := s.data.rdb.ZCard(ctx, countryWeightKey).Result()
		if err == nil && count == 0 {
			// countryMappingKey := s.getLocationMappingKey(supplier, "country", country)
			// s.data.rdb.Del(ctx, countryMappingKey)
			s.data.rdb.Del(ctx, countryWeightKey)
		}
	}
}

// ProcessUserEvent 处理用户事件中的位置映射
// func (s *LocationMappingService) ProcessUserEvent(ctx context.Context, event *types.UserEvent) error {
// 	switch event.Name {
// 	case types.UserEventConnect:
// 		return s.AddDeviceLocationMapping(ctx, event.Supplier, event.Location, event.SdkIp)
// 	case types.UserEventDisconnect:
// 		return s.RemoveDeviceLocationMapping(ctx, event.Supplier, event.Location, event.SdkIp)
// 	default:
// 		return fmt.Errorf("unsupported event type: %s", event.Name)
// 	}
// }

// GetLocationStats 获取位置统计信息
func (s *LocationMappingService) GetLocationStats(ctx context.Context, supplier string) (*LocationStats, error) {
	stats := &LocationStats{
		Supplier:  supplier,
		Countries: make(map[string]*CountryStats),
	}

	// 获取所有国家
	pattern := fmt.Sprintf("location_weight:%s:country:*", supplier)
	keys, err := s.data.rdb.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, err
	}

	for _, key := range keys {
		// 解析国家名
		parts := strings.Split(key, ":")
		if len(parts) < 4 {
			continue
		}
		country := parts[3]

		// 获取国家下的州统计
		countryStats := &CountryStats{
			Name:   country,
			States: make(map[string]*StateStats),
		}

		// 获取州列表和权重
		states, err := s.data.rdb.ZRangeWithScores(ctx, key, 0, -1).Result()
		if err != nil {
			continue
		}

		for _, z := range states {
			state := z.Member.(string)
			stateWeight := int64(z.Score)

			stateStats := &StateStats{
				Name:   state,
				Weight: stateWeight,
				Cities: make(map[string]int64),
			}

			// 获取州下的城市统计
			stateWeightKey := s.getLocationWeightKey(supplier, "state", state)
			cities, err := s.data.rdb.ZRangeWithScores(ctx, stateWeightKey, 0, -1).Result()
			if err == nil {
				for _, cityZ := range cities {
					city := cityZ.Member.(string)
					cityWeight := int64(cityZ.Score)
					stateStats.Cities[city] = cityWeight
				}
			}

			countryStats.States[state] = stateStats
		}

		stats.Countries[country] = countryStats
	}

	return stats, nil
}

// 辅助方法
func (s *LocationMappingService) parseLocation(location string) (country, state, city string) {
	parts := strings.Split(location, ":")
	if len(parts) >= 1 {
		country = parts[0]
	}
	if len(parts) >= 2 {
		state = strings.Join(parts[:2], ":")
	}
	if len(parts) >= 3 {
		city = strings.Join(parts[:3], ":")
	}
	return
}

func (s *LocationMappingService) getLocationMappingKey(supplier, level, location string) string {
	return fmt.Sprintf("location_map:%s:%s:%s", supplier, level, location)
}

func (s *LocationMappingService) getLocationWeightKey(supplier, level, location string) string {
	return fmt.Sprintf("location_weight:%s:%s:%s", supplier, level, location)
}

// 统计数据结构
type LocationStats struct {
	Supplier  string                   `json:"supplier"`
	Countries map[string]*CountryStats `json:"countries"`
}

type CountryStats struct {
	Name   string                 `json:"name"`
	States map[string]*StateStats `json:"states"`
}

type StateStats struct {
	Name   string           `json:"name"`
	Weight int64            `json:"weight"`
	Cities map[string]int64 `json:"cities"`
}

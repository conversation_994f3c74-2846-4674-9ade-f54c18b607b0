package service

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	v1 "sh_proxy/api/user/v1"
	"sh_proxy/app/user/internal/biz"
	"sh_proxy/app/user/internal/data"
	contextlog "sh_proxy/app/user/internal/pkg/log"
	"sh_proxy/pkg/types"
)

type UserService struct {
	v1.UnimplementedUserServer

	log            *contextlog.Helper
	uc             *biz.UserUsecase
	eventPublisher EventPublisher
	data           *data.Data // 添加 data 字段用于访问 Redis
}

func NewUserService(logger log.Logger, uc *biz.UserUsecase, eventPublisher EventPublisher, data *data.Data) *UserService {
	return &UserService{
		log:            contextlog.NewHelper(log.With(logger, "module", "service/user")),
		uc:             uc,
		eventPublisher: eventPublisher,
		data:           data,
	}
}

func (s *UserService) Health(_ context.Context, _ *v1.Empty) (*v1.Empty, error) {
	return &v1.Empty{}, nil
}

func (s *UserService) Event(ctx context.Context, req *v1.EventRequest) (*v1.EventReply, error) {
	// 调用原有的事件处理逻辑
	// reply, err := s.uc.Event(ctx, req)
	if req.Name != types.UserEventConnect && req.Name != types.UserEventDisconnect && req.Name != types.UserEventSdkNotFound && req.Name != types.UserEventCtlUnavailable {
		return nil, fmt.Errorf("event name is invalid,want [sdkConnect, sdkDisconnect, sdkNotFound, ctlUnavailable],got %s", req.Name)
	}

	if req.Name == types.UserEventConnect || req.Name == types.UserEventDisconnect || req.Name == types.UserEventSdkNotFound {
		if req.Supplier == "" {
			return nil, fmt.Errorf("supplier is empty")
		}

		if req.SdkId == "" {
			return nil, fmt.Errorf("sdk_id is empty")
		}
		if req.SdkIp == "" {
			return nil, fmt.Errorf("sdk_ip is empty")
		}
		if req.ConnId == "" {
			return nil, fmt.Errorf("conn_id is empty")
		}
	}

	if req.Name == types.UserEventConnect && req.Host == "" {
		return nil, fmt.Errorf("host is empty")
	}
	if req.Name == types.UserEventCtlUnavailable && req.CtlConnId == "" {
		return nil, fmt.Errorf("ctlConnId is empty")
	}

	if req.Name == types.UserEventCtlUnavailable {
		if err := s.uc.CtlUnavailable(ctx, req.Host, req.CtlConnId); err != nil {
			return nil, err
		}
		return &v1.EventReply{Message: "event ctlunavailable success"}, nil
	}
	// 断连
	// if req.Name == types.UserEventDisconnect || req.Name == types.UserEventSdkNotFound {
	// 	if err := s.uc.HandleDisconnect(ctx, &types.UserEvent{
	// 		Name:      req.Name,
	// 		SdkId:     req.SdkId,
	// 		SdkIp:     req.SdkIp,
	// 		Host:      req.Host,
	// 		Supplier:  req.Supplier,
	// 		ConnId:    req.ConnId,
	// 		CtlConnId: req.CtlConnId,
	// 		TimeStamp: time.Now().UnixMilli(),
	// 		Meta:      req.Meta,
	// 	}); err != nil {
	// 		return nil, err
	// 	}
	// 	return &v1.EventReply{Message: "disConnect success"}, nil
	// }

	// 如果处理成功，异步发布事件到 MQ
	// if err == nil && s.eventPublisher != nil {
	// 异步发布事件（不影响主流程）
	s.publishEventToMQ(ctx, req)
	// }
	// // 所有用户事件都通过MQ异步处理，确保事件顺序
	// if req.Name == types.UserEventConnect || req.Name == types.UserEventDisconnect || req.Name == types.UserEventSdkNotFound {
	// 	// 异步发布事件到 MQ（确保事件顺序）
	// 	s.publishEventToMQ(ctx, req)
	// 	return &v1.EventReply{Message: "event received"}, nil
	// }

	return &v1.EventReply{Message: "event received"}, nil
}

// publishEventToMQ 发布事件到MQ
func (s *UserService) publishEventToMQ(ctx context.Context, req *v1.EventRequest) {
	// 从 context 中获取 trace_id
	traceID := getTraceIDFromContext(ctx)

	// 创建用户事件，添加序列号确保顺序
	userEvent := &types.UserEvent{
		Name:      req.Name,
		SdkId:     req.SdkId,
		SdkIp:     req.SdkIp,
		Host:      req.Host,
		Supplier:  req.Supplier,
		ConnId:    req.ConnId,
		CtlConnId: req.CtlConnId,
		TimeStamp: time.Now().UnixMilli(),
		Meta:      req.Meta,
		Sequence:  s.generateEventSequence(req.SdkId, req.ConnId), // 添加序列号
	}

	// 发布事件
	if err := s.eventPublisher.PublishUserEvent(ctx, userEvent); err != nil {
		s.log.WithContext(ctx).Errorw("msg", "发布用户事件到MQ失败",
			"event_type", req.Name,
			"sdk_id", req.SdkId,
			"trace_id", traceID,
			"error", err,
		)
	} else {
		s.log.WithContext(ctx).Infow("msg", "用户事件已发布到MQ",
			"event_type", req.Name,
			"sdk_id", req.SdkId,
			"trace_id", traceID,
		)
	}
}

func (s *UserService) Route(ctx context.Context, req *v1.RouteRequest) (*v1.RouteReply, error) {
	return s.uc.Route(ctx, req)
}
func (s *UserService) RouteByIp(ctx context.Context, req *v1.RouteByIpRequest) (*v1.RouteByIpReply, error) {
	if req.Supplier == "" {
		return nil, fmt.Errorf("supplier is empty")
	}
	if req.Ip == "" {
		return nil, fmt.Errorf("ip is empty")
	}
	return s.uc.RouteByIp(ctx, req)
}

// generateEventSequence 生成事件序列号，确保同一连接的事件有序
func (s *UserService) generateEventSequence(sdkId, connId string) int64 {
	// 使用 Redis INCR 命令生成全局递增序列号
	ctx := context.Background()
	connKey := fmt.Sprintf("event_seq:%s:%s", sdkId, connId)

	sequence, err := s.data.GetRedisClient().Incr(ctx, connKey).Result()
	if err != nil {
		s.log.Errorw("msg", "生成事件序列号失败，使用时间戳",
			"sdk_id", sdkId,
			"conn_id", connId,
			"error", err,
		)
		// 如果 Redis 失败，使用时间戳作为备选方案
		return time.Now().UnixNano()
	}

	// 设置过期时间（1小时），避免 Redis 中积累过多序列号键
	s.data.GetRedisClient().Expire(ctx, connKey, time.Hour)

	return sequence
}

// 辅助函数
func getTraceIDFromContext(ctx context.Context) string {
	if traceID, ok := ctx.Value("trace_id").(string); ok {
		return traceID
	}
	if traceID, ok := ctx.Value("traceId").(string); ok {
		return traceID
	}
	return ""
}

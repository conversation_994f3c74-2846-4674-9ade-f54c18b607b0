package log

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

// ContextLogger 支持从 context 自动注入 trace_id 的日志器
type ContextLogger struct {
	logger log.Logger
	helper *log.Helper
}

// NewContextLogger 创建支持 context 的日志器
func NewContextLogger(logger log.Logger) *ContextLogger {
	return &ContextLogger{
		logger: logger,
		helper: log.NewHelper(logger),
	}
}

// WithContext 创建带有 context 信息的日志器
func (l *ContextLogger) WithContext(ctx context.Context) *ContextHelper {
	return &ContextHelper{
		ctx:    ctx,
		logger: l.logger,
		helper: l.helper,
	}
}

// ContextHelper 带有 context 的日志助手
type ContextHelper struct {
	ctx    context.Context
	logger log.Logger
	helper *log.Helper
}

// extractContextFields 从 context 中提取字段
func (h *ContextHelper) extractContextFields() []any {
	var fields []any

	// 提取 trace_id
	if traceID, ok := h.ctx.Value("trace_id").(string); ok && traceID != "" {
		fields = append(fields, "trace_id", traceID)
	}

	// 提取 request_id
	if requestID, ok := h.ctx.Value("request_id").(string); ok && requestID != "" {
		fields = append(fields, "request_id", requestID)
	}

	// 可以添加更多的 context 字段
	if userID, ok := h.ctx.Value("user_id").(string); ok && userID != "" {
		fields = append(fields, "user_id", userID)
	}

	if sessionID, ok := h.ctx.Value("session_id").(string); ok && sessionID != "" {
		fields = append(fields, "session_id", sessionID)
	}

	return fields
}

// mergeFields 合并 context 字段和用户提供的字段
func (h *ContextHelper) mergeFields(keyvals ...any) []any {
	contextFields := h.extractContextFields()
	return append(contextFields, keyvals...)
}

// Debug 记录 Debug 级别日志
func (h *ContextHelper) Debug(a ...any) {
	h.helper.Debug(a...)
}

// Debugf 记录格式化的 Debug 级别日志
func (h *ContextHelper) Debugf(format string, a ...any) {
	h.helper.Debugf(format, a...)
}

// Debugw 记录带字段的 Debug 级别日志
func (h *ContextHelper) Debugw(keyvals ...any) {
	mergedFields := h.mergeFields(keyvals...)
	h.helper.Debugw(mergedFields...)
}

// Info 记录 Info 级别日志
func (h *ContextHelper) Info(a ...any) {
	h.helper.Info(a...)
}

// Infof 记录格式化的 Info 级别日志
func (h *ContextHelper) Infof(format string, a ...any) {
	h.helper.Infof(format, a...)
}

// Infow 记录带字段的 Info 级别日志
func (h *ContextHelper) Infow(keyvals ...any) {
	mergedFields := h.mergeFields(keyvals...)
	h.helper.Infow(mergedFields...)
}

// Warn 记录 Warn 级别日志
func (h *ContextHelper) Warn(a ...any) {
	h.helper.Warn(a...)
}

// Warnf 记录格式化的 Warn 级别日志
func (h *ContextHelper) Warnf(format string, a ...any) {
	h.helper.Warnf(format, a...)
}

// Warnw 记录带字段的 Warn 级别日志
func (h *ContextHelper) Warnw(keyvals ...any) {
	mergedFields := h.mergeFields(keyvals...)
	h.helper.Warnw(mergedFields...)
}

// Error 记录 Error 级别日志
func (h *ContextHelper) Error(a ...any) {
	h.helper.Error(a...)
}

// Errorf 记录格式化的 Error 级别日志
func (h *ContextHelper) Errorf(format string, a ...any) {
	h.helper.Errorf(format, a...)
}

// Errorw 记录带字段的 Error 级别日志
func (h *ContextHelper) Errorw(keyvals ...any) {
	mergedFields := h.mergeFields(keyvals...)
	h.helper.Errorw(mergedFields...)
}

// Fatal 记录 Fatal 级别日志
func (h *ContextHelper) Fatal(a ...any) {
	h.helper.Fatal(a...)
}

// Fatalf 记录格式化的 Fatal 级别日志
func (h *ContextHelper) Fatalf(format string, a ...any) {
	h.helper.Fatalf(format, a...)
}

// Fatalw 记录带字段的 Fatal 级别日志
func (h *ContextHelper) Fatalw(keyvals ...any) {
	mergedFields := h.mergeFields(keyvals...)
	h.helper.Fatalw(mergedFields...)
}

// Log 记录日志
func (h *ContextHelper) Log(level log.Level, keyvals ...any) error {
	mergedFields := h.mergeFields(keyvals...)
	h.helper.Log(level, mergedFields...)
	return nil
}

// WithFields 添加额外的字段到日志上下文
func (h *ContextHelper) WithFields(keyvals ...any) *ContextHelper {
	// 创建一个新的 context，包含额外的字段
	newCtx := h.ctx
	for i := 0; i < len(keyvals); i += 2 {
		if i+1 < len(keyvals) {
			key := keyvals[i]
			value := keyvals[i+1]
			newCtx = context.WithValue(newCtx, key, value)
		}
	}

	return &ContextHelper{
		ctx:    newCtx,
		logger: h.logger,
		helper: h.helper,
	}
}

// ContextLoggerWrapper 包装器，用于兼容现有的 log.Helper 接口
type ContextLoggerWrapper struct {
	contextLogger *ContextLogger
	ctx           context.Context
}

// NewContextLoggerWrapper 创建日志包装器
func NewContextLoggerWrapper(logger log.Logger, ctx context.Context) *ContextLoggerWrapper {
	return &ContextLoggerWrapper{
		contextLogger: NewContextLogger(logger),
		ctx:           ctx,
	}
}

// Helper 返回带有 context 的日志助手
func (w *ContextLoggerWrapper) Helper() *ContextHelper {
	return w.contextLogger.WithContext(w.ctx)
}

// UpdateContext 更新 context
func (w *ContextLoggerWrapper) UpdateContext(ctx context.Context) {
	w.ctx = ctx
}

// GetContext 获取当前 context
func (w *ContextLoggerWrapper) GetContext() context.Context {
	return w.ctx
}

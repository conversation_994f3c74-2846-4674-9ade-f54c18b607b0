package biz

import (
	"context"
	"time"

	"github.com/google/wire"
)

// ProviderSet is biz providers.
var ProviderSet = wire.NewSet(NewConsumerUsecase, NewUserUsecase)

// SdkIpinfo is a biz model.
type SdkIpinfo struct {
	IP          string
	Country     string
	CountryCode string
	Region      string
	RegionName  string
	City        string
	District    string
	Zip         string
	Lat         float64
	Lon         float64
	Timezone    string
	Offset      int64
	Currency    string
	Proxy       bool
	Hosting     bool
	Asname      string
	As          string
	ISP         string
	Mobile      bool
	CreatedAt   time.Time
}

type SdkIpinfoRepo interface {
	CreateSdkIpinfo(context.Context, *SdkIpinfo) error
}

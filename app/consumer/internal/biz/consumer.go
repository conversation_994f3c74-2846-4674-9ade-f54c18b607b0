package biz

import (
	"context"

	"github.com/pkg/errors"

	v1 "sh_proxy/api/consumer/v1"
	"sh_proxy/app/consumer/internal/data"
)

type ConsumerUsecase struct {
	data         *data.Data
	ipInfoRepo   data.IPInfoRepo
	eventLogRepo data.EventLogRepo
}

func NewConsumerUsecase(data *data.Data, repo data.IPInfoRepo, eventLogRepo data.EventLogRepo) *ConsumerUsecase {
	return &ConsumerUsecase{
		data:         data,
		ipInfoRepo:   repo,
		eventLogRepo: eventLogRepo,
	}
}

func (c *ConsumerUsecase) IPInfo(ctx context.Context, ip string) (*v1.IPInfoResp, error) {
	ipInfo, isNew, err := c.ipInfoRepo.GetIPInfo(ctx, ip)
	if err != nil {
		return nil, errors.Wrap(err, "ipInfoRepo.GetIPInfo err")
	}

	ipinfoResp := ipInfo2Resp(ipInfo)
	ipinfoResp.IsNew = isNew

	return ipinfoResp, nil
}

func (c *ConsumerUsecase) EventSave(ctx context.Context, req *v1.EventSaveReq) error {
	err := c.eventLogRepo.SaveEventLog(ctx, eventReq2Data(req))
	if err != nil {
		return errors.Wrap(err, "eventLogRepo.SaveEventLog err")
	}

	return nil
}

func eventReq2Data(req *v1.EventSaveReq) *data.EventLog {
	return &data.EventLog{
		Name:      req.Name,
		SdkId:     req.SdkId,
		SdkIp:     req.SdkIp,
		Host:      req.Host,
		Supplier:  req.Supplier,
		ConnId:    req.ConnId,
		CtlConnId: req.CtlConnId,
		TimeStamp: req.Timestamp,
		Meta:      req.Meta,
	}
}

func ipInfo2Resp(info *data.IPInfo) *v1.IPInfoResp {
	return &v1.IPInfoResp{
		Ip:          info.IP,
		Country:     info.Country,
		CountryCode: info.CountryCode,
		Region:      info.Region,
		RegionName:  info.RegionName,
		City:        info.City,
		District:    info.District,
		Zip:         info.Zip,
		Lat:         float32(info.Lat),
		Lon:         float32(info.Lon),
		Timezone:    info.Timezone,
		Isp:         info.ISP,
		Offset:      info.Offset,
		Currency:    info.Currency,
		Proxy:       info.Proxy,
		Hosting:     info.Hosting,
		Asname:      info.Asname,
		As:          info.As,
		Mobile:      info.Mobile,
	}
}

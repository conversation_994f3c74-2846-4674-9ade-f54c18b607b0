package biz

import (
	"context"
	"os"
	"strconv"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"

	"sh_proxy/app/consumer/internal/data"
	contextlog "sh_proxy/pkg/log"
	"sh_proxy/pkg/types"
)

type UserUsecase struct {
	log *contextlog.Helper

	data         *data.Data
	ipInfoRepo   data.IPInfoRepo
	deviceRepo   data.DeviceRepo
	eventLogRepo data.EventLogRepo
}

func NewUserUsecase(logger log.Logger, data *data.Data, ipInfoRepo data.IPInfoRepo, deviceRepo data.DeviceRepo, eventLogRepo data.EventLogRepo) *UserUsecase {
	return &UserUsecase{
		log:          contextlog.NewHelper(logger, "module", "biz/user/consumer"),
		data:         data,
		ipInfoRepo:   ipInfoRepo,
		deviceRepo:   deviceRepo,
		eventLogRepo: eventLogRepo,
	}
}

// handleConnect 处理连接事件
func (ju *UserUsecase) HandleConnect(ctx context.Context, req *types.UserEvent) error {
	// 4. 异步记录连接日志
	go ju.logConnection(req)
	// 使用 WithContext 自动注入 trace_id
	ju.log.WithContext(ctx).Infow("msg", "Handling connect event",
		"sdk_id", req.SdkId,
		"sdk_ip", req.SdkIp,
		"conn_id", req.ConnId,
		"host", req.Host,
		"supplier", req.Supplier,
		"ctl_conn_id", req.CtlConnId,
	)

	var (
		ipInfo *data.IPInfo
		isNew  bool
		err    error
	)

	if os.Getenv("MY_REGION") == "sg" {
		// 1. 查询IP归属地信息
		ipInfo, isNew, err = ju.ipInfoRepo.GetIPInfo(ctx, req.SdkIp)
		if err != nil {
			ju.log.WithContext(ctx).Errorw("msg", "Failed to get IP info",
				"sdk_ip", req.SdkIp,
				"error", err,
			)
			return errors.Wrap(err, "get IP info fail")
		}
	} else {
		ipInfo, isNew, err = ju.ipInfoRepo.GetIPInfoFromSg(ctx, req.SdkIp)
		if err != nil {
			ju.log.WithContext(ctx).Errorw("msg", "Failed to get IP info from sg",
				"sdk_ip", req.SdkIp,
				"error", err,
			)
			return errors.Wrap(err, "get IP info from sg fail")
		}
	}

	// 如果是新 ip，则保存到 clickhouse 中
	if isNew {
		
	}

	// 2. 创建设备信息
	device := &data.DeviceInfo{
		SdkIP:     req.SdkIp,
		Supplier:  req.Supplier,
		Location:  ipInfo.GetLocation(),
		CreatedAt: time.Now(),
	}

	// 3. 添加设备到在线列表
	if err := ju.deviceRepo.AddDevice(ctx, device, req.SdkId, &types.ConnInfo{
		SdkId:     req.SdkId,
		Host:      req.Host,
		ConnId:    req.ConnId,
		CtlConnId: req.CtlConnId,
		Timestamp: req.TimeStamp,
	}); err != nil {
		ju.log.WithContext(ctx).Errorw("msg", "Failed to add device",
			"sdk_id", req.SdkId,
			"sdk_ip", req.SdkIp,
			"conn_id", req.ConnId,
			"error", err,
		)
		return errors.Wrap(err, "failed to add device")
	}

	return nil
}

// HandleDisconnect 处理断连事件
func (ju *UserUsecase) HandleDisconnect(ctx context.Context, req *types.UserEvent) error {
	go ju.logDisconnection(req)

	ju.log.WithContext(ctx).Infow("msg", "Handling disconnect event",
		"sdk_id", req.SdkId,
		"sdk_ip", req.SdkIp,
		"conn_id", req.ConnId,
		"supplier", req.Supplier,
	)

	// 从在线设备中移除连接
	if err := ju.deviceRepo.RemoveDevice(ctx, req.Supplier, req.SdkId, req.SdkIp, req.ConnId, req.CtlConnId); err != nil {
		ju.log.WithContext(ctx).Errorw("msg", "Failed to remove device",
			"sdk_id", req.SdkId,
			"sdk_ip", req.SdkIp,
			"conn_id", req.ConnId,
			"error", err,
		)
		return errors.Wrap(err, "failed to remove device")
	}

	return nil
}

// HandleCtlUnavailable handles the ctlUnavailable event
func (ju *UserUsecase) HandleCtlUnavailable(ctx context.Context, req *types.UserEvent) error {
	ju.log.WithContext(ctx).Infow("msg", "Handling ctlUnavailable event",
		"ctl_conn_id", req.CtlConnId,
		"host", req.Host,
	)

	// Mark the ctlConnId as unavailable in Redis
	if err := ju.deviceRepo.SetCtlUnavailable(ctx, req.CtlConnId); err != nil {
		ju.log.WithContext(ctx).Errorw("msg", "Failed to set ctl unavailable",
			"ctl_conn_id", req.CtlConnId,
			"error", err,
		)
		return errors.Wrap(err, "failed to set ctl unavailable")
	}

	return nil
}

// logConnection 异步记录连接日志
func (ju *UserUsecase) logConnection(req *types.UserEvent) {
	// 注意：这里是异步调用，没有原始的 context，所以使用普通的日志方法
	ju.log.Infow(
		"msg", "event log",
		"sdk_id", req.SdkId,
		"sdk_ip", req.SdkIp,
		"conn_id", req.ConnId,
	)
	if os.Getenv("MY_REGION") == "sg" {
		// 写入 mongo
		ju.eventLogRepo.SaveEventLog(context.Background(), userEventToLog(req))
	} else {
		ju.eventLogRepo.PushEventLog(context.Background(), userEventToLog(req))
	}
}

// logDisconnection 异步记录断连日志
func (ju *UserUsecase) logDisconnection(req *types.UserEvent) {
	// 注意：这里是异步调用，没有原始的 context，所以使用普通的日志方法
	ju.log.Infow(
		"msg", "Connection log",
		"sdk_id", req.SdkId,
		"sdk_ip", req.SdkIp,
		"conn_id", req.ConnId,
	)
	if os.Getenv("MY_REGION") == "sg" {
		// 写入 mongo
		ju.eventLogRepo.SaveEventLog(context.Background(), userEventToLog(req))
	} else {
		ju.eventLogRepo.PushEventLog(context.Background(), userEventToLog(req))
	}
}

func userEventToLog(event *types.UserEvent) *data.EventLog {
	return &data.EventLog{
		Name:      event.Name,
		SdkId:     event.SdkId,
		SdkIp:     event.SdkIp,
		Host:      event.Host,
		Supplier:  event.Supplier,
		ConnId:    event.ConnId,
		CtlConnId: event.CtlConnId,
		TimeStamp: strconv.FormatInt(event.TimeStamp, 10),
		Meta:      event.Meta,
	}
}

// GetUnavailableCtlConns retrieves all ctlConnIds from the unavailable set.
func (ju *UserUsecase) GetUnavailableCtlConns(ctx context.Context) ([]string, error) {
	return ju.data.GetUnavailableCtlConns(ctx)
}

// RemoveConnectionsByCtlConnIdV2 will trigger the removal of connections using the new Lua script implementation.
func (ju *UserUsecase) RemoveConnectionsByCtlConnIdV2(ctx context.Context, ctlConnId string) error {
	return ju.deviceRepo.RemoveConnectionsByCtlConnIdHash(ctx, ctlConnId)
	// return ju.deviceRepo.RemoveConnectionsByCtlConnId(ctx, ctlConnId)
}

package server

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"sh_proxy/app/consumer/internal/data"
	contextlog "sh_proxy/pkg/log"
)

// LifecycleManager 应用生命周期管理器
type LifecycleManager struct {
	log               *contextlog.Helper
	clickhouseBatcher *data.ClickHouseBatchWriterV2
}

// NewLifecycleManager 创建生命周期管理器
func NewLifecycleManager(logger log.Logger, clickhouseBatcher *data.ClickHouseBatchWriterV2) *LifecycleManager {
	return &LifecycleManager{
		log:               contextlog.NewHelper(log.With(logger, "module", "lifecycle_manager")),
		clickhouseBatcher: clickhouseBatcher,
	}
}

// Start 启动所有组件
func (lm *LifecycleManager) Start(ctx context.Context) error {
	lm.log.Info("启动应用组件...")

	// 启动ClickHouse批量写入器
	if lm.clickhouseBatcher != nil {
		lm.clickhouseBatcher.Start()
		lm.log.Info("ClickHouse批量写入器已启动")
	}

	lm.log.Info("所有应用组件启动完成")
	return nil
}

// Stop 停止所有组件
func (lm *LifecycleManager) Stop(ctx context.Context) error {
	lm.log.Info("正在停止应用组件...")

	// 创建带超时的上下文
	stopCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// 停止ClickHouse批量写入器
	if lm.clickhouseBatcher != nil {
		lm.log.Info("正在停止ClickHouse批量写入器...")
		if err := lm.clickhouseBatcher.Stop(stopCtx); err != nil {
			lm.log.Errorw("msg", "停止ClickHouse批量写入器失败", "error", err)
			return err
		}
		lm.log.Info("ClickHouse批量写入器已停止")
	}

	lm.log.Info("所有应用组件停止完成")
	return nil
}

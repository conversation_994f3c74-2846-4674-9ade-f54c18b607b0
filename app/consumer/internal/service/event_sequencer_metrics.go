package service

import (
	"sync/atomic"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	contextlog "sh_proxy/pkg/log"
)

// EventSequencerMetrics 事件排序器指标
type EventSequencerMetrics struct {
	// 处理统计
	TotalEvents     int64 `json:"total_events"`
	ProcessedEvents int64 `json:"processed_events"`
	DroppedEvents   int64 `json:"dropped_events"`
	SyncProcessed   int64 `json:"sync_processed"`  // 同步处理数量
	AsyncProcessed  int64 `json:"async_processed"` // 异步处理数量

	// 缓冲区统计
	ActiveBuffers   int64 `json:"active_buffers"`
	TotalBufferSize int64 `json:"total_buffer_size"`
	MaxBufferSize   int64 `json:"max_buffer_size"`

	// 性能统计
	AvgProcessTime int64 `json:"avg_process_time_ns"`
	MaxProcessTime int64 `json:"max_process_time_ns"`
	QueueLength    int64 `json:"queue_length"`

	// 错误统计
	ProcessErrors int64 `json:"process_errors"`
	TimeoutEvents int64 `json:"timeout_events"`

	// 工作协程统计
	ActiveWorkers     int64 `json:"active_workers"`
	WorkerUtilization int64 `json:"worker_utilization"` // 百分比
}

// MetricsCollector 指标收集器
type MetricsCollector struct {
	log       *contextlog.Helper
	sequencer *EventSequencer
	metrics   *EventSequencerMetrics
	stopCh    chan struct{}
}

// NewMetricsCollector 创建指标收集器
func NewMetricsCollector(logger log.Logger, sequencer *EventSequencer) *MetricsCollector {
	return &MetricsCollector{
		log:       contextlog.NewHelper(log.With(logger, "module", "metrics_collector")),
		sequencer: sequencer,
		metrics:   &EventSequencerMetrics{},
		stopCh:    make(chan struct{}),
	}
}

// Start 启动指标收集
func (mc *MetricsCollector) Start() {
	go mc.collectRoutine()
}

// Stop 停止指标收集
func (mc *MetricsCollector) Stop() {
	close(mc.stopCh)
}

// GetMetrics 获取当前指标
func (mc *MetricsCollector) GetMetrics() *EventSequencerMetrics {
	return mc.metrics
}

// IncrementTotalEvents 增加总事件数
func (mc *MetricsCollector) IncrementTotalEvents() {
	atomic.AddInt64(&mc.metrics.TotalEvents, 1)
}

// IncrementProcessedEvents 增加已处理事件数
func (mc *MetricsCollector) IncrementProcessedEvents() {
	atomic.AddInt64(&mc.metrics.ProcessedEvents, 1)
}

// IncrementDroppedEvents 增加丢弃事件数
func (mc *MetricsCollector) IncrementDroppedEvents() {
	atomic.AddInt64(&mc.metrics.DroppedEvents, 1)
}

// IncrementSyncProcessed 增加同步处理数
func (mc *MetricsCollector) IncrementSyncProcessed() {
	atomic.AddInt64(&mc.metrics.SyncProcessed, 1)
}

// IncrementAsyncProcessed 增加异步处理数
func (mc *MetricsCollector) IncrementAsyncProcessed() {
	atomic.AddInt64(&mc.metrics.AsyncProcessed, 1)
}

// IncrementProcessErrors 增加处理错误数
func (mc *MetricsCollector) IncrementProcessErrors() {
	atomic.AddInt64(&mc.metrics.ProcessErrors, 1)
}

// IncrementTimeoutEvents 增加超时事件数
func (mc *MetricsCollector) IncrementTimeoutEvents() {
	atomic.AddInt64(&mc.metrics.TimeoutEvents, 1)
}

// RecordProcessTime 记录处理时间
func (mc *MetricsCollector) RecordProcessTime(duration time.Duration) {
	ns := duration.Nanoseconds()

	// 更新平均处理时间（简单移动平均）
	currentAvg := atomic.LoadInt64(&mc.metrics.AvgProcessTime)
	newAvg := (currentAvg + ns) / 2
	atomic.StoreInt64(&mc.metrics.AvgProcessTime, newAvg)

	// 更新最大处理时间
	for {
		currentMax := atomic.LoadInt64(&mc.metrics.MaxProcessTime)
		if ns <= currentMax {
			break
		}
		if atomic.CompareAndSwapInt64(&mc.metrics.MaxProcessTime, currentMax, ns) {
			break
		}
	}
}

// collectRoutine 指标收集协程
func (mc *MetricsCollector) collectRoutine() {
	ticker := time.NewTicker(10 * time.Second) // 每10秒收集一次
	defer ticker.Stop()

	for {
		select {
		case <-mc.stopCh:
			return
		case <-ticker.C:
			mc.collectMetrics()
		}
	}
}

// collectMetrics 收集指标
func (mc *MetricsCollector) collectMetrics() {
	// 收集缓冲区统计
	activeBuffers := int64(0)
	totalBufferSize := int64(0)
	maxBufferSize := int64(0)

	for _, shard := range mc.sequencer.connectionBuffers {
		shard.mutex.RLock()
		activeBuffers += int64(len(shard.buffers))

		for _, buffer := range shard.buffers {
			buffer.mutex.Lock()
			bufferSize := int64(len(buffer.events))
			totalBufferSize += bufferSize
			if bufferSize > maxBufferSize {
				maxBufferSize = bufferSize
			}
			buffer.mutex.Unlock()
		}
		shard.mutex.RUnlock()
	}

	atomic.StoreInt64(&mc.metrics.ActiveBuffers, activeBuffers)
	atomic.StoreInt64(&mc.metrics.TotalBufferSize, totalBufferSize)
	atomic.StoreInt64(&mc.metrics.MaxBufferSize, maxBufferSize)

	// 收集队列长度
	queueLength := int64(len(mc.sequencer.eventChan))
	atomic.StoreInt64(&mc.metrics.QueueLength, queueLength)

	// 收集工作协程统计
	atomic.StoreInt64(&mc.metrics.ActiveWorkers, int64(mc.sequencer.workerCount))

	// 计算工作协程利用率（基于队列长度）
	utilization := (queueLength * 100) / int64(cap(mc.sequencer.eventChan))
	if utilization > 100 {
		utilization = 100
	}
	atomic.StoreInt64(&mc.metrics.WorkerUtilization, utilization)

	// 定期输出指标日志
	mc.logMetrics()
}

// logMetrics 输出指标日志
func (mc *MetricsCollector) logMetrics() {
	metrics := mc.metrics

	mc.log.Infow("msg", "事件排序器指标",
		"total_events", atomic.LoadInt64(&metrics.TotalEvents),
		"processed_events", atomic.LoadInt64(&metrics.ProcessedEvents),
		"dropped_events", atomic.LoadInt64(&metrics.DroppedEvents),
		"sync_processed", atomic.LoadInt64(&metrics.SyncProcessed),
		"async_processed", atomic.LoadInt64(&metrics.AsyncProcessed),
		"active_buffers", atomic.LoadInt64(&metrics.ActiveBuffers),
		"total_buffer_size", atomic.LoadInt64(&metrics.TotalBufferSize),
		"max_buffer_size", atomic.LoadInt64(&metrics.MaxBufferSize),
		"queue_length", atomic.LoadInt64(&metrics.QueueLength),
		"avg_process_time_ms", atomic.LoadInt64(&metrics.AvgProcessTime)/1000000,
		"max_process_time_ms", atomic.LoadInt64(&metrics.MaxProcessTime)/1000000,
		"process_errors", atomic.LoadInt64(&metrics.ProcessErrors),
		"timeout_events", atomic.LoadInt64(&metrics.TimeoutEvents),
		"worker_utilization", atomic.LoadInt64(&metrics.WorkerUtilization),
	)
}

// GetHealthStatus 获取健康状态
func (mc *MetricsCollector) GetHealthStatus() map[string]interface{} {
	metrics := mc.metrics

	queueLength := atomic.LoadInt64(&metrics.QueueLength)
	queueCapacity := int64(cap(mc.sequencer.eventChan))
	utilization := atomic.LoadInt64(&metrics.WorkerUtilization)
	errorRate := float64(atomic.LoadInt64(&metrics.ProcessErrors)) / float64(atomic.LoadInt64(&metrics.TotalEvents)) * 100

	status := "healthy"
	if queueLength > queueCapacity*8/10 { // 队列使用率超过80%
		status = "warning"
	}
	if utilization > 90 || errorRate > 5 { // 利用率超过90%或错误率超过5%
		status = "critical"
	}

	return map[string]interface{}{
		"status":             status,
		"queue_usage":        float64(queueLength) / float64(queueCapacity) * 100,
		"worker_utilization": utilization,
		"error_rate":         errorRate,
		"active_buffers":     atomic.LoadInt64(&metrics.ActiveBuffers),
		"total_events":       atomic.LoadInt64(&metrics.TotalEvents),
	}
}

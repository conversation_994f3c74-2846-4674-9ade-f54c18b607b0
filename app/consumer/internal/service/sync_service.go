package service

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"gorm.io/gorm"

	"sh_proxy/app/consumer/internal/conf"
	"sh_proxy/app/consumer/internal/data/models"
	contextlog "sh_proxy/pkg/log"
)

// MongoIPInfo MongoDB中的IP信息结构
type MongoIPInfo struct {
	IP          string    `bson:"ip"`
	Country     string    `bson:"country"`
	CountryCode string    `bson:"countryCode"`
	Region      string    `bson:"region"`
	RegionName  string    `bson:"regionName"`
	City        string    `bson:"city"`
	District    string    `bson:"district"`
	Zip         string    `bson:"zip"`
	Lat         float64   `bson:"lat"`
	Lon         float64   `bson:"lon"`
	Timezone    string    `bson:"timezone"`
	Offset      int       `bson:"offset"`
	Currency    string    `bson:"currency"`
	Proxy       bool      `bson:"proxy"`
	Hosting     bool      `bson:"hosting"`
	Asname      string    `bson:"asname"`
	As          string    `bson:"as"`
	ISP         string    `bson:"isp"`
	Mobile      bool      `bson:"mobile"`
	CreatedAt   time.Time `bson:"createdAt"`
	UpdatedAt   time.Time `bson:"updatedAt"`
}

// SyncService 数据同步服务
type SyncService struct {
	log          *contextlog.Helper
	mongoClient  *mongo.Client
	clickhouseDB *gorm.DB
	mdb          *mongo.Database
	config       *conf.Data

	// 同步状态
	syncOnce  sync.Once
	syncDone  bool
	syncError error
}

// NewSyncService 创建同步服务
func NewSyncService(logger log.Logger, mongoClient *mongo.Client, clickhouseDB *gorm.DB, config *conf.Data) *SyncService {
	ss := &SyncService{
		log:          contextlog.NewHelper(log.With(logger, "module", "sync_service")),
		mongoClient:  mongoClient,
		clickhouseDB: clickhouseDB,
		config:       config,
	}
	myRegion := os.Getenv("MY_REGION")
	if myRegion == "sg" || myRegion == "" {
		ss.mdb = mongoClient.Database(config.Mongodb.DbName)
	}
	return ss
}

// StartupSync 启动时执行一次性同步
func (s *SyncService) StartupSync(ctx context.Context) error {
	var err error

	s.syncOnce.Do(func() {
		s.log.Info("开始启动时数据同步...")

		// 检查是否需要同步
		if !s.shouldSync(ctx) {
			s.log.Info("跳过数据同步：ClickHouse中已有数据")
			s.syncDone = true
			return
		}

		// 执行同步
		err = s.performSync(ctx)
		if err != nil {
			s.syncError = err
			s.log.Errorw("msg", "启动时数据同步失败", "error", err)
			return
		}

		s.syncDone = true
		s.log.Info("启动时数据同步完成")
	})

	return err
}

// shouldSync 检查是否需要同步
func (s *SyncService) shouldSync(ctx context.Context) bool {
	// 检查ClickHouse中是否已有数据
	var count int64
	if err := s.clickhouseDB.WithContext(ctx).Model(&models.SdkIpinfo{}).Count(&count).Error; err != nil {
		s.log.Warnw("msg", "检查ClickHouse数据失败，将执行同步", "error", err)
		return true
	}

	s.log.Infow("msg", "ClickHouse中现有记录数", "count", count)

	// 如果ClickHouse中没有数据，则需要同步
	return count == 0
}

// performSync 执行数据同步
func (s *SyncService) performSync(ctx context.Context) error {
	start := time.Now()

	// 获取MongoDB集合
	collection := s.mdb.Collection("ip_info")

	// 统计总数
	totalCount, err := collection.CountDocuments(ctx, bson.M{})
	if err != nil {
		return fmt.Errorf("统计MongoDB记录数失败: %w", err)
	}

	s.log.Infow("msg", "开始同步MongoDB数据到ClickHouse", "total_count", totalCount)

	if totalCount == 0 {
		s.log.Info("MongoDB中没有数据，跳过同步")
		return nil
	}

	// 分批处理
	batchSize := 1000

	var processedCount int64
	var insertedCount int64
	var errorCount int64

	// 创建游标
	cursor, err := collection.Find(ctx, bson.M{}, options.Find().SetBatchSize(int32(batchSize)))
	if err != nil {
		return fmt.Errorf("创建MongoDB游标失败: %w", err)
	}
	defer cursor.Close(ctx)

	batch := make([]*models.SdkIpinfo, 0, batchSize)

	for cursor.Next(ctx) {
		var mongoRecord MongoIPInfo
		if err := cursor.Decode(&mongoRecord); err != nil {
			s.log.Warnw("msg", "解码MongoDB记录失败", "error", err)
			errorCount++
			continue
		}

		// 转换为ClickHouse格式
		clickhouseRecord := s.convertToClickHouseModel(&mongoRecord)
		batch = append(batch, clickhouseRecord)
		processedCount++

		// 批量插入
		if len(batch) >= batchSize {
			inserted, err := s.insertBatch(ctx, batch)
			if err != nil {
				s.log.Errorw("msg", "批量插入失败", "error", err)
				errorCount += int64(len(batch))
			} else {
				insertedCount += int64(inserted)
			}

			batch = batch[:0] // 清空批次

			// 进度报告
			progress := float64(processedCount) / float64(totalCount) * 100
			s.log.Infow("msg", "同步进度",
				"progress", fmt.Sprintf("%.2f%%", progress),
				"processed", processedCount,
				"total", totalCount,
				"inserted", insertedCount,
				"errors", errorCount,
			)
		}
	}

	// 处理剩余的记录
	if len(batch) > 0 {
		inserted, err := s.insertBatch(ctx, batch)
		if err != nil {
			s.log.Errorw("msg", "最后批次插入失败", "error", err)
			errorCount += int64(len(batch))
		} else {
			insertedCount += int64(inserted)
		}
	}

	if err := cursor.Err(); err != nil {
		return fmt.Errorf("MongoDB游标错误: %w", err)
	}

	duration := time.Since(start)
	s.log.Infow("msg", "数据同步完成",
		"duration", duration,
		"processed", processedCount,
		"inserted", insertedCount,
		"errors", errorCount,
		"rate", fmt.Sprintf("%.2f records/sec", float64(processedCount)/duration.Seconds()),
	)

	return nil
}

// createClickHouseTable 创建ClickHouse表
func (s *SyncService) createClickHouseTable(ctx context.Context) error {
	// 使用GORM自动迁移
	if err := s.clickhouseDB.WithContext(ctx).AutoMigrate(&models.SdkIpinfo{}); err != nil {
		return fmt.Errorf("ClickHouse表自动迁移失败: %w", err)
	}

	s.log.Info("ClickHouse表创建/检查完成")
	return nil
}

// convertToClickHouseModel 转换MongoDB模型到ClickHouse模型
func (s *SyncService) convertToClickHouseModel(mongoRecord *MongoIPInfo) *models.SdkIpinfo {
	return &models.SdkIpinfo{
		IP:          mongoRecord.IP,
		Country:     mongoRecord.Country,
		CountryCode: mongoRecord.CountryCode,
		Region:      mongoRecord.Region,
		RegionName:  mongoRecord.RegionName,
		City:        mongoRecord.City,
		District:    mongoRecord.District,
		Zip:         mongoRecord.Zip,
		Lat:         mongoRecord.Lat,
		Lon:         mongoRecord.Lon,
		Timezone:    mongoRecord.Timezone,
		Offset:      int64(mongoRecord.Offset),
		Currency:    mongoRecord.Currency,
		Proxy:       mongoRecord.Proxy,
		Hosting:     mongoRecord.Hosting,
		Asname:      mongoRecord.Asname,
		As:          mongoRecord.As,
		ISP:         mongoRecord.ISP,
		Mobile:      mongoRecord.Mobile,
		CreatedAt:   mongoRecord.CreatedAt,
	}
}

// insertBatch 批量插入数据
func (s *SyncService) insertBatch(ctx context.Context, records []*models.SdkIpinfo) (int, error) {
	if len(records) == 0 {
		return 0, nil
	}

	// 使用GORM批量插入
	result := s.clickhouseDB.WithContext(ctx).CreateInBatches(records, len(records))
	if result.Error != nil {
		return 0, result.Error
	}

	return int(result.RowsAffected), nil
}

// GetSyncStatus 获取同步状态
func (s *SyncService) GetSyncStatus() (bool, error) {
	return s.syncDone, s.syncError
}

// IsSyncCompleted 检查同步是否完成
func (s *SyncService) IsSyncCompleted() bool {
	return s.syncDone
}

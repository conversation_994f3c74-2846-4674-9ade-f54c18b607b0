package service

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"sh_proxy/app/consumer/internal/biz"
	"sh_proxy/app/consumer/internal/data"
	contextlog "sh_proxy/pkg/log"
	"sh_proxy/pkg/types"
)

// MessageHandler 消息处理器接口
type MessageHandler interface {
	Handle(ctx context.Context, msg *types.MQMessage) error
	Type() string
}

// ConsumerService 消息消费服务
type ConsumerService struct {
	log       *contextlog.Helper
	ctx       context.Context
	cancel    context.CancelFunc
	startOnce sync.Once
	stopOnce  sync.Once
	running   bool
	wg        sync.WaitGroup

	// 消息处理器映射
	handlers map[string]MessageHandler

	data *data.Data
	uc   *biz.UserUsecase
}

// NewConsumerService 创建任务结果消费服务
func NewConsumerService(uc *biz.UserUsecase, data *data.Data, logger log.Logger) (*ConsumerService, error) {
	logHelper := contextlog.NewHelper(log.With(logger, "module", "service/consumer"))
	ctx, cancel := context.WithCancel(context.Background())

	service := &ConsumerService{
		uc:       uc,
		data:     data,
		log:      logHelper,
		ctx:      ctx,
		cancel:   cancel,
		handlers: make(map[string]MessageHandler),
	}

	// 注册所有处理器
	service.registerHandlers()

	return service, nil
}

// 注册所有处理器
func (s *ConsumerService) registerHandlers() {
	// 创建用户事件处理器
	userEventHandler := &UserEventHandler{
		log: contextlog.NewHelper(log.With(s.log.Logger(), "module", "userevent-handler")),
		uc:  s.uc,
	}

	// 创建事件排序器
	sequencer := NewEventSequencer(s.log.Logger(), userEventHandler)
	userEventHandler.sequencer = sequencer

	// 启动事件排序器
	sequencer.Start()

	// 注册用户事件处理器
	s.registerHandler(userEventHandler)

	s.log.Info("消息处理器注册完成")
}

// registerHandler 注册消息处理器
func (s *ConsumerService) registerHandler(handler MessageHandler) {
	s.handlers[handler.Type()] = handler
	s.log.Infof("注册消息处理器: %s", handler.Type())
}

// Start 启动消费服务
func (s *ConsumerService) Start() error {
	var err error
	s.startOnce.Do(func() {
		s.log.Info("正在启动消息消费服务...")

		// 标记为运行中
		s.running = true

		// 启动消息消费
		s.wg.Add(1)
		go func() {
			defer s.wg.Done()
			err = s.startMessageConsumer()
			if err != nil {
				s.log.Errorf("消息消费服务出错: %v", err)
			}
		}()

		s.wg.Add(1)
		go s.StartCtlUnavailableCleaner(s.ctx)

		s.log.Info("消息消费服务启动成功")
	})
	return err
}

// Stop 停止消费服务
func (s *ConsumerService) Stop() {
	s.stopOnce.Do(func() {
		if !s.running {
			return
		}

		s.log.Info("正在停止消息消费服务...")

		s.cancel() // 触发上下文取消
		s.running = false

		// 等待所有 goroutine 完成
		s.wg.Wait()

		s.log.Info("消息消费服务已停止")
	})
}

// startMessageConsumer 启动统一消息消费者
func (s *ConsumerService) startMessageConsumer() error {
	s.log.Info("启动统一消息消费者...")

	// 使用现有的队列消费消息
	return s.data.MQConn.ConsumeMessages(s.ctx, s.data.MQConn.GetQueue(), s.handleMQMessage)
}

// handleMQMessage 处理MQ消息
func (s *ConsumerService) handleMQMessage(body []byte) error {
	startTime := time.Now()
	ctx := context.Background()

	// 解析MQMessage
	var mqMsg types.MQMessage
	if err := json.Unmarshal(body, &mqMsg); err != nil {
		s.log.Errorf("解析MQ消息失败: %v, body: %s", err, string(body))
		return err
	}
	ctx = context.WithValue(ctx, "trace_id", mqMsg.TraceId)

	s.log.Infow("msg", "收到MQ消息",
		"type", mqMsg.Type,
		"trace_id", mqMsg.TraceId,
		"retry", mqMsg.Retry,
		"timestamp", mqMsg.Timestamp,
	)

	// 查找对应的处理器
	handler, ok := s.handlers[mqMsg.Type]
	if !ok {
		s.log.Warnf("未找到消息处理器: %s", mqMsg.Type)
		return fmt.Errorf("未找到消息处理器: %s", mqMsg.Type)
	}

	// 处理消息
	err := handler.Handle(ctx, &mqMsg)
	duration := time.Since(startTime)

	if err != nil {
		s.log.Errorw("msg", "处理MQ消息失败",
			"type", mqMsg.Type,
			"trace_id", mqMsg.TraceId,
			"error", err,
			"duration_ms", duration.Milliseconds(),
			"retry", mqMsg.Retry,
		)

		// 如果还有重试次数，重新发送到队列
		// if mqMsg.ShouldRetry() {
		// 	return s.retryMessage(&mqMsg)
		// }

		// 超过最大重试次数，记录到死信
		s.handleDeadLetterMessage(&mqMsg, err)
		return err
	}

	s.log.Infow("msg", "MQ消息处理成功",
		"type", mqMsg.Type,
		"trace_id", mqMsg.TraceId,
		"duration_ms", duration.Milliseconds(),
	)

	return nil
}

// retryMessage 重试消息
func (s *ConsumerService) retryMessage(msg *types.MQMessage) error {
	msg.IncrementRetry()

	// 计算延迟时间（指数退避）
	delay := time.Duration(msg.Retry*msg.Retry) * time.Second
	if delay > 60*time.Second {
		delay = 60 * time.Second
	}

	s.log.Infow("msg", "重试MQ消息",
		"type", msg.Type,
		"trace_id", msg.TraceId,
		"retry", msg.Retry,
		"delay_seconds", delay.Seconds(),
	)

	// 延迟后重新发送
	time.AfterFunc(delay, func() {
		body := msg.Marshal()
		s.data.MQConn.PublishMessage("", s.data.MQConn.GetQueue(), body)
	})

	return nil
}

// handleDeadLetterMessage 处理死信消息
func (s *ConsumerService) handleDeadLetterMessage(msg *types.MQMessage, err error) {
	s.log.Errorw("msg", "MQ消息进入死信",
		"type", msg.Type,
		"trace_id", msg.TraceId,
		"retry", msg.Retry,
		"max_retry", msg.MaxRetry,
		"message", string(msg.Marshal()),
		"error", err.Error(),
	)

	// TODO: 可以将死信消息存储到数据库或发送告警
}

// StartCtlUnavailableCleaner starts a background task to clean up connections for unavailable ctlConnIds.
func (s *ConsumerService) StartCtlUnavailableCleaner(ctx context.Context) {
	defer s.wg.Done()
	ticker := time.NewTicker(1 * time.Second) // Adjust the interval as needed
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			s.log.Info("Stopping CtlUnavailable cleaner")
			return
		case <-ticker.C:
			// s.log.Info("Running CtlUnavailable cleaner task")
			unavailableIds, err := s.uc.GetUnavailableCtlConns(ctx)
			if err != nil {
				s.log.Errorf("Failed to get unavailable ctl connections: %v", err)
				continue
			}

			for _, ctlConnId := range unavailableIds {
				s.log.Infof("Cleaning up connections for unavailable ctlConnId: %s", ctlConnId)
				if err := s.uc.RemoveConnectionsByCtlConnIdV2(ctx, ctlConnId); err != nil {
					s.log.Errorf("Failed to clean up connections for ctlConnId %s: %v", ctlConnId, err)
				}
			}
		}
	}
}

// // handleJobResult 处理任务结果
// func (s *ConsumerService) handleJobResult(body []byte) error {
// 	// 解析JSON格式的消息体
// 	var msg types.ConsumerHandleRes
// 	if err := json.Unmarshal(body, &msg); err != nil {
// 		return errors.Wrapf(err, "unmarshal msg body failed: %s", body)
// 	}
//
// 	s.log.Infof("收到消息: type=%s, jobID=%d, pid=%d", msg.HandlerType, msg.JobId, msg.Pid)
//
// 	// 根据处理器类型选择相应的处理器
// 	handler, ok := s.handlers[msg.HandlerType]
// 	if !ok {
// 		s.log.Warnf("未找到处理器: %s", msg.HandlerType)
// 		return errors.Errorf("未找到处理器: %s", msg.HandlerType)
// 	}
//
// 	// 处理消息
// 	result, err := handler.HandleResult(&msg)
// 	if err != nil {
// 		s.log.Errorf("处理结果失败: %v", err)
// 		return err
// 	}
//
// 	return nil
// }

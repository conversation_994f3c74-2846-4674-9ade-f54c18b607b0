package service

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"sh_proxy/app/consumer/internal/data"
	contextlog "sh_proxy/pkg/log"
)

// OnlineTimeService 在线时间服务
type OnlineTimeService struct {
	log            *contextlog.Helper
	onlineTimeRepo data.OnlineTimeRepo
}

// NewOnlineTimeService 创建在线时间服务
func NewOnlineTimeService(logger log.Logger, onlineTimeRepo data.OnlineTimeRepo) *OnlineTimeService {
	return &OnlineTimeService{
		log:            contextlog.NewHelper(log.With(logger, "module", "service/online_time")),
		onlineTimeRepo: onlineTimeRepo,
	}
}

// OnlineTimeStatsResponse 在线时间统计响应
type OnlineTimeStatsResponse struct {
	SdkId            string `json:"sdkId"`
	SdkIP            string `json:"sdkIp"`
	Supplier         string `json:"supplier"`
	Location         string `json:"location"`
	TotalSessions    int64  `json:"totalSessions"`
	TotalOnlineTime  int64  `json:"totalOnlineTime"`  // 秒
	AvgOnlineTime    int64  `json:"avgOnlineTime"`    // 秒
	LastConnected    string `json:"lastConnected"`    // 格式化时间
	LastDisconnected string `json:"lastDisconnected"` // 格式化时间
	CurrentSessions  int64  `json:"currentSessions"`
	
	// 格式化的时间字段
	TotalOnlineTimeFormatted string `json:"totalOnlineTimeFormatted"` // 如: "2h 30m 45s"
	AvgOnlineTimeFormatted   string `json:"avgOnlineTimeFormatted"`   // 如: "15m 30s"
}

// GetDeviceOnlineStats 获取设备在线时间统计
func (s *OnlineTimeService) GetDeviceOnlineStats(ctx context.Context, supplier, sdkIP string) (*OnlineTimeStatsResponse, error) {
	stats, err := s.onlineTimeRepo.GetDeviceOnlineStats(ctx, supplier, sdkIP)
	if err != nil {
		s.log.WithContext(ctx).Errorw("msg", "获取设备在线统计失败",
			"supplier", supplier,
			"sdk_ip", sdkIP,
			"error", err,
		)
		return nil, err
	}
	
	return s.formatStatsResponse(stats), nil
}

// GetSDKOnlineStats 获取SDK在线时间统计
func (s *OnlineTimeService) GetSDKOnlineStats(ctx context.Context, supplier, sdkIP, sdkId string) (*OnlineTimeStatsResponse, error) {
	stats, err := s.onlineTimeRepo.GetSDKOnlineStats(ctx, supplier, sdkIP, sdkId)
	if err != nil {
		s.log.WithContext(ctx).Errorw("msg", "获取SDK在线统计失败",
			"supplier", supplier,
			"sdk_ip", sdkIP,
			"sdk_id", sdkId,
			"error", err,
		)
		return nil, err
	}
	
	return s.formatStatsResponse(stats), nil
}

// GetBatchOnlineStats 批量获取在线时间统计
func (s *OnlineTimeService) GetBatchOnlineStats(ctx context.Context, supplier string, sdkIPs []string) (map[string]*OnlineTimeStatsResponse, error) {
	statsMap, err := s.onlineTimeRepo.GetBatchOnlineStats(ctx, supplier, sdkIPs)
	if err != nil {
		s.log.WithContext(ctx).Errorw("msg", "批量获取在线统计失败",
			"supplier", supplier,
			"sdk_ips_count", len(sdkIPs),
			"error", err,
		)
		return nil, err
	}
	
	result := make(map[string]*OnlineTimeStatsResponse)
	for ip, stats := range statsMap {
		result[ip] = s.formatStatsResponse(stats)
	}
	
	return result, nil
}

// formatStatsResponse 格式化统计响应
func (s *OnlineTimeService) formatStatsResponse(stats *data.OnlineTimeStats) *OnlineTimeStatsResponse {
	response := &OnlineTimeStatsResponse{
		SdkId:           stats.SdkId,
		SdkIP:           stats.SdkIP,
		Supplier:        stats.Supplier,
		Location:        stats.Location,
		TotalSessions:   stats.TotalSessions,
		TotalOnlineTime: stats.TotalOnlineTime,
		AvgOnlineTime:   stats.AvgOnlineTime,
		CurrentSessions: stats.CurrentSessions,
	}
	
	// 格式化时间
	if stats.LastConnected > 0 {
		response.LastConnected = time.Unix(stats.LastConnected, 0).Format("2006-01-02 15:04:05")
	}
	
	if stats.LastDisconnected > 0 {
		response.LastDisconnected = time.Unix(stats.LastDisconnected, 0).Format("2006-01-02 15:04:05")
	}
	
	// 格式化在线时长
	response.TotalOnlineTimeFormatted = s.formatDuration(stats.TotalOnlineTime)
	response.AvgOnlineTimeFormatted = s.formatDuration(stats.AvgOnlineTime)
	
	return response
}

// formatDuration 格式化时长
func (s *OnlineTimeService) formatDuration(seconds int64) string {
	if seconds <= 0 {
		return "0s"
	}
	
	duration := time.Duration(seconds) * time.Second
	
	days := int(duration.Hours()) / 24
	hours := int(duration.Hours()) % 24
	minutes := int(duration.Minutes()) % 60
	secs := int(duration.Seconds()) % 60
	
	var parts []string
	
	if days > 0 {
		parts = append(parts, fmt.Sprintf("%dd", days))
	}
	if hours > 0 {
		parts = append(parts, fmt.Sprintf("%dh", hours))
	}
	if minutes > 0 {
		parts = append(parts, fmt.Sprintf("%dm", minutes))
	}
	if secs > 0 || len(parts) == 0 {
		parts = append(parts, fmt.Sprintf("%ds", secs))
	}
	
	result := ""
	for i, part := range parts {
		if i > 0 {
			result += " "
		}
		result += part
		if i >= 2 { // 最多显示3个单位
			break
		}
	}
	
	return result
}

// GetOnlineTimeReport 获取在线时间报告
func (s *OnlineTimeService) GetOnlineTimeReport(ctx context.Context, supplier string, startTime, endTime int64) (map[string]interface{}, error) {
	// 这里可以扩展为更复杂的报告逻辑
	// 比如按时间段统计、按地区统计等
	
	report := map[string]interface{}{
		"supplier":   supplier,
		"startTime":  time.Unix(startTime, 0).Format("2006-01-02 15:04:05"),
		"endTime":    time.Unix(endTime, 0).Format("2006-01-02 15:04:05"),
		"generated":  time.Now().Format("2006-01-02 15:04:05"),
		"summary": map[string]interface{}{
			"totalDevices":     0,
			"totalSessions":    0,
			"totalOnlineTime":  0,
			"avgOnlineTime":    0,
			"activeDevices":    0,
		},
	}
	
	s.log.WithContext(ctx).Infow("msg", "生成在线时间报告",
		"supplier", supplier,
		"start_time", startTime,
		"end_time", endTime,
	)
	
	return report, nil
}

// GetRealTimeStats 获取实时统计
func (s *OnlineTimeService) GetRealTimeStats(ctx context.Context, supplier string) (map[string]interface{}, error) {
	now := time.Now()
	
	stats := map[string]interface{}{
		"supplier":   supplier,
		"timestamp":  now.Unix(),
		"time":       now.Format("2006-01-02 15:04:05"),
		"realtime": map[string]interface{}{
			"currentOnlineDevices": 0,
			"currentSessions":      0,
			"todayNewConnections":  0,
			"todayDisconnections":  0,
		},
	}
	
	s.log.WithContext(ctx).Infow("msg", "获取实时统计",
		"supplier", supplier,
	)
	
	return stats, nil
}

// CleanupExpiredStats 清理过期统计数据
func (s *OnlineTimeService) CleanupExpiredStats(ctx context.Context, retentionDays int) error {
	// 清理超过保留期的统计数据
	cutoffTime := time.Now().AddDate(0, 0, -retentionDays).Unix()
	
	s.log.WithContext(ctx).Infow("msg", "开始清理过期统计数据",
		"retention_days", retentionDays,
		"cutoff_time", time.Unix(cutoffTime, 0).Format("2006-01-02 15:04:05"),
	)
	
	// 这里可以实现具体的清理逻辑
	// 比如删除过期的统计记录
	
	return nil
}

// ExportStats 导出统计数据
func (s *OnlineTimeService) ExportStats(ctx context.Context, supplier string, format string) ([]byte, error) {
	s.log.WithContext(ctx).Infow("msg", "导出统计数据",
		"supplier", supplier,
		"format", format,
	)
	
	// 这里可以实现CSV、Excel等格式的导出
	switch format {
	case "csv":
		return s.exportCSV(ctx, supplier)
	case "json":
		return s.exportJSON(ctx, supplier)
	default:
		return nil, fmt.Errorf("unsupported export format: %s", format)
	}
}

// exportCSV 导出CSV格式
func (s *OnlineTimeService) exportCSV(ctx context.Context, supplier string) ([]byte, error) {
	// CSV导出实现
	csv := "SdkIP,SdkId,TotalSessions,TotalOnlineTime,AvgOnlineTime,CurrentSessions\n"
	// 这里添加实际数据
	
	return []byte(csv), nil
}

// exportJSON 导出JSON格式
func (s *OnlineTimeService) exportJSON(ctx context.Context, supplier string) ([]byte, error) {
	// JSON导出实现
	data := map[string]interface{}{
		"supplier":    supplier,
		"exportTime":  time.Now().Format("2006-01-02 15:04:05"),
		"data":        []interface{}{},
	}
	
	// 这里添加实际数据序列化
	return []byte("{}"), nil
}

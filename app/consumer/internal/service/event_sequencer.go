package service

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"sh_proxy/pkg/types"
	contextlog "sh_proxy/pkg/log"
)

// EventSequencer 事件排序器，确保同一连接的事件按序处理
type EventSequencer struct {
	log *contextlog.Helper
	
	// 每个连接的事件缓冲区
	connectionBuffers map[string]*ConnectionEventBuffer
	bufferMutex       sync.RWMutex
	
	// 事件处理器
	handler EventHandler
	
	// 配置
	maxBufferSize    int           // 最大缓冲区大小
	maxWaitTime      time.Duration // 最大等待时间
	cleanupInterval  time.Duration // 清理间隔
	
	// 停止信号
	stopCh chan struct{}
}

// ConnectionEventBuffer 连接事件缓冲区
type ConnectionEventBuffer struct {
	connKey        string                    // 连接标识 (supplier:sdkId:connId)
	events         []*types.UserEvent        // 事件缓冲区
	lastProcessed  int64                     // 最后处理的序列号
	lastActivity   time.Time                 // 最后活动时间
	mutex          sync.Mutex                // 缓冲区锁
}

// EventHandler 事件处理器接口
type EventHandler interface {
	HandleEvent(ctx context.Context, event *types.UserEvent) error
}

// NewEventSequencer 创建事件排序器
func NewEventSequencer(logger log.Logger, handler EventHandler) *EventSequencer {
	return &EventSequencer{
		log:               contextlog.NewHelper(log.With(logger, "module", "event_sequencer")),
		connectionBuffers: make(map[string]*ConnectionEventBuffer),
		handler:           handler,
		maxBufferSize:     100,
		maxWaitTime:       5 * time.Second,
		cleanupInterval:   30 * time.Second,
		stopCh:            make(chan struct{}),
	}
}

// Start 启动事件排序器
func (es *EventSequencer) Start() {
	go es.cleanupRoutine()
}

// Stop 停止事件排序器
func (es *EventSequencer) Stop() {
	close(es.stopCh)
}

// ProcessEvent 处理事件（确保顺序）
func (es *EventSequencer) ProcessEvent(ctx context.Context, event *types.UserEvent) error {
	connKey := es.getConnectionKey(event)
	
	es.bufferMutex.Lock()
	buffer, exists := es.connectionBuffers[connKey]
	if !exists {
		buffer = &ConnectionEventBuffer{
			connKey:       connKey,
			events:        make([]*types.UserEvent, 0),
			lastProcessed: 0,
			lastActivity:  time.Now(),
		}
		es.connectionBuffers[connKey] = buffer
	}
	es.bufferMutex.Unlock()
	
	return es.addEventToBuffer(ctx, buffer, event)
}

// addEventToBuffer 添加事件到缓冲区并尝试处理
func (es *EventSequencer) addEventToBuffer(ctx context.Context, buffer *ConnectionEventBuffer, event *types.UserEvent) error {
	buffer.mutex.Lock()
	defer buffer.mutex.Unlock()
	
	buffer.lastActivity = time.Now()
	
	// 如果事件序列号为0或者小于等于已处理的序列号，直接处理
	if event.Sequence == 0 || event.Sequence <= buffer.lastProcessed {
		es.log.WithContext(ctx).Warnw("msg", "收到过期或无序列号事件，直接处理",
			"conn_key", buffer.connKey,
			"event_sequence", event.Sequence,
			"last_processed", buffer.lastProcessed,
			"event_type", event.Name,
		)
		return es.handler.HandleEvent(ctx, event)
	}
	
	// 添加到缓冲区
	buffer.events = append(buffer.events, event)
	
	// 检查缓冲区大小
	if len(buffer.events) > es.maxBufferSize {
		es.log.WithContext(ctx).Warnw("msg", "事件缓冲区已满，强制处理",
			"conn_key", buffer.connKey,
			"buffer_size", len(buffer.events),
		)
		return es.forceProcessBuffer(ctx, buffer)
	}
	
	// 尝试处理有序事件
	return es.processOrderedEvents(ctx, buffer)
}

// processOrderedEvents 处理有序事件
func (es *EventSequencer) processOrderedEvents(ctx context.Context, buffer *ConnectionEventBuffer) error {
	// 按序列号排序
	sort.Slice(buffer.events, func(i, j int) bool {
		return buffer.events[i].Sequence < buffer.events[j].Sequence
	})
	
	processed := 0
	for i, event := range buffer.events {
		// 检查是否是下一个期望的序列号
		if event.Sequence == buffer.lastProcessed+1 {
			if err := es.handler.HandleEvent(ctx, event); err != nil {
				es.log.WithContext(ctx).Errorw("msg", "处理事件失败",
					"conn_key", buffer.connKey,
					"event_sequence", event.Sequence,
					"event_type", event.Name,
					"error", err,
				)
				return err
			}
			
			buffer.lastProcessed = event.Sequence
			processed = i + 1
			
			es.log.WithContext(ctx).Infow("msg", "事件处理成功",
				"conn_key", buffer.connKey,
				"event_sequence", event.Sequence,
				"event_type", event.Name,
			)
		} else {
			// 遇到乱序事件，停止处理
			break
		}
	}
	
	// 移除已处理的事件
	if processed > 0 {
		buffer.events = buffer.events[processed:]
	}
	
	return nil
}

// forceProcessBuffer 强制处理缓冲区中的所有事件
func (es *EventSequencer) forceProcessBuffer(ctx context.Context, buffer *ConnectionEventBuffer) error {
	// 按序列号排序
	sort.Slice(buffer.events, func(i, j int) bool {
		return buffer.events[i].Sequence < buffer.events[j].Sequence
	})
	
	// 处理所有事件
	for _, event := range buffer.events {
		if err := es.handler.HandleEvent(ctx, event); err != nil {
			es.log.WithContext(ctx).Errorw("msg", "强制处理事件失败",
				"conn_key", buffer.connKey,
				"event_sequence", event.Sequence,
				"event_type", event.Name,
				"error", err,
			)
		} else {
			buffer.lastProcessed = event.Sequence
		}
	}
	
	// 清空缓冲区
	buffer.events = buffer.events[:0]
	
	return nil
}

// getConnectionKey 获取连接标识
func (es *EventSequencer) getConnectionKey(event *types.UserEvent) string {
	return fmt.Sprintf("%s:%s:%s", event.Supplier, event.SdkId, event.ConnId)
}

// cleanupRoutine 清理过期的缓冲区
func (es *EventSequencer) cleanupRoutine() {
	ticker := time.NewTicker(es.cleanupInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-es.stopCh:
			return
		case <-ticker.C:
			es.cleanup()
		}
	}
}

// cleanup 清理过期的缓冲区
func (es *EventSequencer) cleanup() {
	es.bufferMutex.Lock()
	defer es.bufferMutex.Unlock()
	
	now := time.Now()
	for connKey, buffer := range es.connectionBuffers {
		buffer.mutex.Lock()
		
		// 检查是否过期
		if now.Sub(buffer.lastActivity) > es.maxWaitTime {
			// 强制处理剩余事件
			if len(buffer.events) > 0 {
				es.log.Infow("msg", "清理过期缓冲区，强制处理剩余事件",
					"conn_key", connKey,
					"remaining_events", len(buffer.events),
				)
				es.forceProcessBuffer(context.Background(), buffer)
			}
			
			// 删除缓冲区
			delete(es.connectionBuffers, connKey)
		}
		
		buffer.mutex.Unlock()
	}
}

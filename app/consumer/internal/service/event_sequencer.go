package service

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	contextlog "sh_proxy/pkg/log"
	"sh_proxy/pkg/types"
)

// EventSequencer 事件排序器，确保同一连接的事件按序处理
type EventSequencer struct {
	log *contextlog.Helper

	// 每个连接的事件缓冲区 - 使用分片减少锁竞争
	connectionBuffers []*ConnectionBufferShard
	shardCount        int

	// 事件处理器
	handler EventHandler

	// 配置
	maxBufferSize   int           // 最大缓冲区大小
	maxWaitTime     time.Duration // 最大等待时间
	cleanupInterval time.Duration // 清理间隔
	workerCount     int           // 工作协程数量

	// 工作队列
	eventChan chan *EventTask

	// 停止信号
	stopCh chan struct{}
	wg     sync.WaitGroup
}

// ConnectionBufferShard 连接缓冲区分片，减少锁竞争
type ConnectionBufferShard struct {
	buffers map[string]*ConnectionEventBuffer
	mutex   sync.RWMutex
}

// EventTask 事件任务
type EventTask struct {
	ctx   context.Context
	event *types.UserEvent
	done  chan error
}

// ConnectionEventBuffer 连接事件缓冲区
type ConnectionEventBuffer struct {
	connKey        string             // 连接标识 (supplier:sdkId:connId)
	events         []*types.UserEvent // 事件缓冲区
	lastProcessed  int64              // 最后处理的序列号
	lastActivity   time.Time          // 最后活动时间
	mutex          sync.Mutex         // 缓冲区锁
	processing     bool               // 是否正在处理
	processingCond *sync.Cond         // 处理条件变量
}

// EventHandler 事件处理器接口
type EventHandler interface {
	HandleEvent(ctx context.Context, event *types.UserEvent) error
}

// NewEventSequencer 创建事件排序器
func NewEventSequencer(logger log.Logger, handler EventHandler) *EventSequencer {
	shardCount := 64  // 64个分片，减少锁竞争
	workerCount := 32 // 32个工作协程

	shards := make([]*ConnectionBufferShard, shardCount)
	for i := 0; i < shardCount; i++ {
		shards[i] = &ConnectionBufferShard{
			buffers: make(map[string]*ConnectionEventBuffer),
		}
	}

	return &EventSequencer{
		log:               contextlog.NewHelper(log.With(logger, "module", "event_sequencer")),
		connectionBuffers: shards,
		shardCount:        shardCount,
		handler:           handler,
		maxBufferSize:     1000,             // 增加缓冲区大小
		maxWaitTime:       2 * time.Second,  // 减少等待时间
		cleanupInterval:   10 * time.Second, // 更频繁的清理
		workerCount:       workerCount,
		eventChan:         make(chan *EventTask, 10000), // 大容量队列
		stopCh:            make(chan struct{}),
	}
}

// Start 启动事件排序器
func (es *EventSequencer) Start() {
	// 启动工作协程池
	for i := 0; i < es.workerCount; i++ {
		es.wg.Add(1)
		go es.worker(i)
	}

	// 启动清理协程
	es.wg.Add(1)
	go es.cleanupRoutine()

	es.log.Infow("msg", "事件排序器启动",
		"shard_count", es.shardCount,
		"worker_count", es.workerCount,
		"max_buffer_size", es.maxBufferSize,
	)
}

// Stop 停止事件排序器
func (es *EventSequencer) Stop() {
	close(es.stopCh)
	close(es.eventChan)
	es.wg.Wait()
	es.log.Info("事件排序器已停止")
}

// ProcessEvent 处理事件（确保顺序）- 异步版本
func (es *EventSequencer) ProcessEvent(ctx context.Context, event *types.UserEvent) error {
	// 创建事件任务
	task := &EventTask{
		ctx:   ctx,
		event: event,
		done:  make(chan error, 1),
	}

	// 提交到工作队列
	select {
	case es.eventChan <- task:
		// 等待处理完成
		return <-task.done
	case <-ctx.Done():
		return ctx.Err()
	default:
		// 队列满了，直接同步处理
		es.log.Warnw("msg", "事件队列已满，降级为同步处理",
			"event_type", event.Name,
			"conn_key", es.getConnectionKey(event),
		)
		return es.processEventSync(ctx, event)
	}
}

// processEventSync 同步处理事件（降级方案）
func (es *EventSequencer) processEventSync(ctx context.Context, event *types.UserEvent) error {
	connKey := es.getConnectionKey(event)
	shard := es.getShard(connKey)

	shard.mutex.Lock()
	buffer, exists := shard.buffers[connKey]
	if !exists {
		buffer = es.createBuffer(connKey)
		shard.buffers[connKey] = buffer
	}
	shard.mutex.Unlock()

	return es.addEventToBuffer(ctx, buffer, event)
}

// addEventToBuffer 添加事件到缓冲区并尝试处理
func (es *EventSequencer) addEventToBuffer(ctx context.Context, buffer *ConnectionEventBuffer, event *types.UserEvent) error {
	buffer.mutex.Lock()
	defer buffer.mutex.Unlock()

	buffer.lastActivity = time.Now()

	// 如果事件序列号为0或者小于等于已处理的序列号，直接处理
	if event.Sequence == 0 || event.Sequence <= buffer.lastProcessed {
		es.log.WithContext(ctx).Warnw("msg", "收到过期或无序列号事件，直接处理",
			"conn_key", buffer.connKey,
			"event_sequence", event.Sequence,
			"last_processed", buffer.lastProcessed,
			"event_type", event.Name,
		)
		return es.handler.HandleEvent(ctx, event)
	}

	// 添加到缓冲区
	buffer.events = append(buffer.events, event)

	// 检查缓冲区大小
	if len(buffer.events) > es.maxBufferSize {
		es.log.WithContext(ctx).Warnw("msg", "事件缓冲区已满，强制处理",
			"conn_key", buffer.connKey,
			"buffer_size", len(buffer.events),
		)
		return es.forceProcessBuffer(ctx, buffer)
	}

	// 尝试处理有序事件
	return es.processOrderedEvents(ctx, buffer)
}

// processOrderedEvents 处理有序事件
func (es *EventSequencer) processOrderedEvents(ctx context.Context, buffer *ConnectionEventBuffer) error {
	// 按序列号排序
	sort.Slice(buffer.events, func(i, j int) bool {
		return buffer.events[i].Sequence < buffer.events[j].Sequence
	})

	processed := 0
	for i, event := range buffer.events {
		// 检查是否是下一个期望的序列号
		if event.Sequence == buffer.lastProcessed+1 {
			if err := es.handler.HandleEvent(ctx, event); err != nil {
				es.log.WithContext(ctx).Errorw("msg", "处理事件失败",
					"conn_key", buffer.connKey,
					"event_sequence", event.Sequence,
					"event_type", event.Name,
					"error", err,
				)
				return err
			}

			buffer.lastProcessed = event.Sequence
			processed = i + 1

			es.log.WithContext(ctx).Infow("msg", "事件处理成功",
				"conn_key", buffer.connKey,
				"event_sequence", event.Sequence,
				"event_type", event.Name,
			)
		} else {
			// 遇到乱序事件，停止处理
			break
		}
	}

	// 移除已处理的事件
	if processed > 0 {
		buffer.events = buffer.events[processed:]
	}

	return nil
}

// forceProcessBuffer 强制处理缓冲区中的所有事件
func (es *EventSequencer) forceProcessBuffer(ctx context.Context, buffer *ConnectionEventBuffer) error {
	// 按序列号排序
	sort.Slice(buffer.events, func(i, j int) bool {
		return buffer.events[i].Sequence < buffer.events[j].Sequence
	})

	// 处理所有事件
	for _, event := range buffer.events {
		if err := es.handler.HandleEvent(ctx, event); err != nil {
			es.log.WithContext(ctx).Errorw("msg", "强制处理事件失败",
				"conn_key", buffer.connKey,
				"event_sequence", event.Sequence,
				"event_type", event.Name,
				"error", err,
			)
		} else {
			buffer.lastProcessed = event.Sequence
		}
	}

	// 清空缓冲区
	buffer.events = buffer.events[:0]

	return nil
}

// worker 工作协程
func (es *EventSequencer) worker(id int) {
	defer es.wg.Done()

	es.log.Infow("msg", "工作协程启动", "worker_id", id)

	for {
		select {
		case <-es.stopCh:
			es.log.Infow("msg", "工作协程停止", "worker_id", id)
			return
		case task, ok := <-es.eventChan:
			if !ok {
				es.log.Infow("msg", "工作协程退出", "worker_id", id)
				return
			}

			err := es.processEventSync(task.ctx, task.event)
			task.done <- err
		}
	}
}

// getShard 获取分片
func (es *EventSequencer) getShard(connKey string) *ConnectionBufferShard {
	hash := es.hash(connKey)
	return es.connectionBuffers[hash%es.shardCount]
}

// hash 简单哈希函数
func (es *EventSequencer) hash(s string) int {
	h := 0
	for _, c := range s {
		h = h*31 + int(c)
	}
	if h < 0 {
		h = -h
	}
	return h
}

// createBuffer 创建缓冲区
func (es *EventSequencer) createBuffer(connKey string) *ConnectionEventBuffer {
	buffer := &ConnectionEventBuffer{
		connKey:       connKey,
		events:        make([]*types.UserEvent, 0),
		lastProcessed: 0,
		lastActivity:  time.Now(),
	}
	buffer.processingCond = sync.NewCond(&buffer.mutex)
	return buffer
}

// getConnectionKey 获取连接标识
func (es *EventSequencer) getConnectionKey(event *types.UserEvent) string {
	return fmt.Sprintf("%s:%s:%s", event.Supplier, event.SdkId, event.ConnId)
}

// cleanupRoutine 清理过期的缓冲区
func (es *EventSequencer) cleanupRoutine() {
	defer es.wg.Done()

	ticker := time.NewTicker(es.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-es.stopCh:
			es.log.Info("清理协程停止")
			return
		case <-ticker.C:
			es.cleanup()
		}
	}
}

// cleanup 清理过期的缓冲区
func (es *EventSequencer) cleanup() {
	now := time.Now()
	cleanedCount := 0

	// 并行清理所有分片
	var wg sync.WaitGroup
	for i, shard := range es.connectionBuffers {
		wg.Add(1)
		go func(shardIndex int, s *ConnectionBufferShard) {
			defer wg.Done()
			es.cleanupShard(s, now, shardIndex)
		}(i, shard)
	}
	wg.Wait()

	if cleanedCount > 0 {
		es.log.Infow("msg", "清理完成", "cleaned_buffers", cleanedCount)
	}
}

// cleanupShard 清理单个分片
func (es *EventSequencer) cleanupShard(shard *ConnectionBufferShard, now time.Time, shardIndex int) {
	shard.mutex.Lock()
	defer shard.mutex.Unlock()

	toDelete := make([]string, 0)

	for connKey, buffer := range shard.buffers {
		buffer.mutex.Lock()

		// 检查是否过期且未在处理中
		if !buffer.processing && now.Sub(buffer.lastActivity) > es.maxWaitTime {
			// 强制处理剩余事件
			if len(buffer.events) > 0 {
				es.log.Infow("msg", "清理过期缓冲区，强制处理剩余事件",
					"shard", shardIndex,
					"conn_key", connKey,
					"remaining_events", len(buffer.events),
				)
				es.forceProcessBuffer(context.Background(), buffer)
			}

			toDelete = append(toDelete, connKey)
		}

		buffer.mutex.Unlock()
	}

	// 删除过期缓冲区
	for _, connKey := range toDelete {
		delete(shard.buffers, connKey)
	}
}

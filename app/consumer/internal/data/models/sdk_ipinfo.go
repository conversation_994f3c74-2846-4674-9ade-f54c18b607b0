package models

import "time"

type SdkIpinfo struct {
	IP          string  `gorm:"column:ip;type:String"`
	Country     string  `gorm:"country;type:String" json:"country"`
	CountryCode string  `gorm:"country_code;type:String" json:"country_code"`
	Region      string  `gorm:"region;type:String" json:"region"`
	RegionName  string  `gorm:"region_name;type:String" json:"region_name"`
	City        string  `gorm:"city;type:String" json:"city"`
	District    string  `gorm:"district;type:String" json:"district"`
	Zip         string  `gorm:"zip;type:String" json:"zip"`
	Lat         float64 `gorm:"lat;type:String" json:"lat"`
	Lon         float64 `gorm:"lon;type:String" json:"lon"`
	Timezone    string  `gorm:"timezone;type:String" json:"timezone"`
	Offset      int64   `gorm:"offset;type:String" json:"offset"`
	Currency    string  `gorm:"currency;type:String" json:"currency"`
	Proxy       bool    `gorm:"proxy;type:Bool" json:"proxy"`
	Hosting     bool    `gorm:"hosting;type:Bool" json:"hosting"`
	Asname      string  `gorm:"asname;type:String" json:"asname"`
	As          string  `gorm:"as;type:String" json:"as"`
	ISP         string  `gorm:"isp;type:String" json:"isp"`
	Mobile      bool    `gorm:"mobile;type:Bool" json:"mobile"`

	CreatedAt time.Time `gorm:"created_at;type:DateTime" json:"created_at"`
}

func (SdkIpinfo) TableName() string {
	return "sdk_ipinfo"
}

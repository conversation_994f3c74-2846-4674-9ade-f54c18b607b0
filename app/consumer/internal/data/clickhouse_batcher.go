package data

import (
	"context"
	"sync"
	"time"

	"sh_proxy/app/consumer/internal/data/models"

	"github.com/go-kratos/kratos/v2/log"
)

const (
	defaultBatchSize = 10000
	defaultFrequency = 1 * time.Minute
)

// ClickHouseBatcher 负责批量将 SdkIpinfo 写入 ClickHouse
type ClickHouseBatcher struct {
	log    *log.Helper
	writer func(context.Context, []*models.SdkIpinfo) error

	buffer  []*models.SdkIpinfo
	mu      sync.Mutex
	ticker  *time.Ticker
	closeCh chan struct{}
	wg      sync.WaitGroup
}

// NewClickHouseBatcher 创建一个新的批处理器实例
func NewClickHouseBatcher(writer func(context.Context, []*models.SdkIpinfo) error, logger log.Logger) (*ClickHouseBatcher, func()) {
	b := &ClickHouseBatcher{
		log:     log.NewHelper(log.With(logger, "module", "data/clickhouse-batcher")),
		writer:  writer,
		buffer:  make([]*models.SdkIpinfo, 0, defaultBatchSize),
		ticker:  time.NewTicker(defaultFrequency),
		closeCh: make(chan struct{}),
	}

	b.wg.Add(1)
	go b.run()

	cleanup := func() {
		b.log.Info("Stopping ClickHouse batcher...")
		b.stop()
		b.log.Info("ClickHouse batcher stopped.")
	}

	return b, cleanup
}

// Add 将一个 SdkIpinfo 添加到缓冲区
func (b *ClickHouseBatcher) Add(info *models.SdkIpinfo) {
	b.mu.Lock()
	b.buffer = append(b.buffer, info)
	// 检查是否达到批量大小
	if len(b.buffer) >= defaultBatchSize {
		// 异步 flush，避免阻塞 Add 调用
		go b.flush()
	}
	b.mu.Unlock()
}

// run 是后台运行的 goroutine，用于定时刷新缓冲区
func (b *ClickHouseBatcher) run() {
	defer b.wg.Done()
	for {
		select {
		case <-b.ticker.C:
			b.flush()
		case <-b.closeCh:
			// 收到关闭信号，停止 ticker 并执行最后一次 flush
			b.ticker.Stop()
			b.flush()
			return
		}
	}
}

// stop 用于优雅停机
func (b *ClickHouseBatcher) stop() {
	close(b.closeCh) // 发送关闭信号
	b.wg.Wait()      // 等待 run goroutine 退出
}

// flush 将缓冲区的数据写入 ClickHouse
func (b *ClickHouseBatcher) flush() {
	b.mu.Lock()
	if len(b.buffer) == 0 {
		b.mu.Unlock()
		return
	}

	// 复制缓冲区内容，以便可以立即释放锁
	toWrite := make([]*models.SdkIpinfo, len(b.buffer))
	copy(toWrite, b.buffer)
	// 重置缓冲区
	b.buffer = b.buffer[:0]
	b.mu.Unlock()

	if len(toWrite) > 0 {
		b.log.Infof("Flushing %d records to ClickHouse...", len(toWrite))
		// 调用实际的写入方法
		if err := b.writer(context.Background(), toWrite); err != nil {
			b.log.Errorf("Failed to flush records to ClickHouse: %v", err)
			// 在实际生产中，这里可能需要错误处理逻辑，比如数据回退或重试
		} else {
			b.log.Infof("Successfully flushed %d records to ClickHouse.", len(toWrite))
		}
	}
}

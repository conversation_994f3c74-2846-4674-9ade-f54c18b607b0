package data

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"
	"sh_proxy/pkg/types"
)

// OnlineTimeStats 在线时间统计
type OnlineTimeStats struct {
	SdkId          string `json:"sdkId"`
	SdkIP          string `json:"sdkIp"`
	Supplier       string `json:"supplier"`
	Location       string `json:"location"`
	TotalSessions  int64  `json:"totalSessions"`  // 总会话数
	TotalOnlineTime int64 `json:"totalOnlineTime"` // 总在线时间（秒）
	AvgOnlineTime  int64  `json:"avgOnlineTime"`   // 平均在线时间（秒）
	LastConnected  int64  `json:"lastConnected"`   // 最后连接时间
	LastDisconnected int64 `json:"lastDisconnected"` // 最后断连时间
	CurrentSessions int64 `json:"currentSessions"`  // 当前在线会话数
}

// OnlineTimeRepo 在线时间仓库接口
type OnlineTimeRepo interface {
	// 记录连接事件
	RecordConnect(ctx context.Context, event *types.UserEvent) error
	// 记录断连事件
	RecordDisconnect(ctx context.Context, event *types.UserEvent) error
	// 获取设备在线时间统计
	GetDeviceOnlineStats(ctx context.Context, supplier, sdkIP string) (*OnlineTimeStats, error)
	// 获取SDK在线时间统计
	GetSDKOnlineStats(ctx context.Context, supplier, sdkIP, sdkId string) (*OnlineTimeStats, error)
	// 批量获取在线时间统计
	GetBatchOnlineStats(ctx context.Context, supplier string, sdkIPs []string) (map[string]*OnlineTimeStats, error)
}

// onlineTimeRepo 在线时间仓库实现
type onlineTimeRepo struct {
	data *Data
}

// NewOnlineTimeRepo 创建在线时间仓库
func NewOnlineTimeRepo(data *Data) OnlineTimeRepo {
	return &onlineTimeRepo{
		data: data,
	}
}

// RecordConnect 记录连接事件
func (r *onlineTimeRepo) RecordConnect(ctx context.Context, event *types.UserEvent) error {
	statsKey := r.getDeviceStatsKey(event.Supplier, event.SdkIp)
	sdkStatsKey := r.getSDKStatsKey(event.Supplier, event.SdkIp, event.SdkId)
	
	now := time.Now().Unix()
	
	// 使用Lua脚本原子性地更新统计信息
	return r.recordConnectWithLua(ctx, statsKey, sdkStatsKey, event, now)
}

// RecordDisconnect 记录断连事件
func (r *onlineTimeRepo) RecordDisconnect(ctx context.Context, event *types.UserEvent) error {
	statsKey := r.getDeviceStatsKey(event.Supplier, event.SdkIp)
	sdkStatsKey := r.getSDKStatsKey(event.Supplier, event.SdkIp, event.SdkId)
	
	now := time.Now().Unix()
	
	// 使用Lua脚本原子性地更新统计信息
	return r.recordDisconnectWithLua(ctx, statsKey, sdkStatsKey, event, now)
}

// GetDeviceOnlineStats 获取设备在线时间统计
func (r *onlineTimeRepo) GetDeviceOnlineStats(ctx context.Context, supplier, sdkIP string) (*OnlineTimeStats, error) {
	statsKey := r.getDeviceStatsKey(supplier, sdkIP)
	
	data, err := r.data.rdb.HGetAll(ctx, statsKey).Result()
	if err != nil {
		if err == redis.Nil {
			return &OnlineTimeStats{
				SdkIP:    sdkIP,
				Supplier: supplier,
			}, nil
		}
		return nil, err
	}
	
	return r.parseOnlineStats(data, sdkIP, supplier, "")
}

// GetSDKOnlineStats 获取SDK在线时间统计
func (r *onlineTimeRepo) GetSDKOnlineStats(ctx context.Context, supplier, sdkIP, sdkId string) (*OnlineTimeStats, error) {
	statsKey := r.getSDKStatsKey(supplier, sdkIP, sdkId)
	
	data, err := r.data.rdb.HGetAll(ctx, statsKey).Result()
	if err != nil {
		if err == redis.Nil {
			return &OnlineTimeStats{
				SdkId:    sdkId,
				SdkIP:    sdkIP,
				Supplier: supplier,
			}, nil
		}
		return nil, err
	}
	
	return r.parseOnlineStats(data, sdkIP, supplier, sdkId)
}

// GetBatchOnlineStats 批量获取在线时间统计
func (r *onlineTimeRepo) GetBatchOnlineStats(ctx context.Context, supplier string, sdkIPs []string) (map[string]*OnlineTimeStats, error) {
	if len(sdkIPs) == 0 {
		return make(map[string]*OnlineTimeStats), nil
	}
	
	// 构建所有键
	keys := make([]string, len(sdkIPs))
	for i, ip := range sdkIPs {
		keys[i] = r.getDeviceStatsKey(supplier, ip)
	}
	
	// 使用Pipeline批量获取
	pipe := r.data.rdb.Pipeline()
	cmds := make([]*redis.MapStringStringCmd, len(keys))
	for i, key := range keys {
		cmds[i] = pipe.HGetAll(ctx, key)
	}
	
	_, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, err
	}
	
	// 解析结果
	result := make(map[string]*OnlineTimeStats)
	for i, cmd := range cmds {
		data, err := cmd.Result()
		if err != nil && err != redis.Nil {
			continue
		}
		
		stats, err := r.parseOnlineStats(data, sdkIPs[i], supplier, "")
		if err != nil {
			continue
		}
		
		result[sdkIPs[i]] = stats
	}
	
	return result, nil
}

// recordConnectWithLua 使用Lua脚本记录连接事件
func (r *onlineTimeRepo) recordConnectWithLua(ctx context.Context, statsKey, sdkStatsKey string, event *types.UserEvent, timestamp int64) error {
	const script = `
-- 参数说明:
-- KEYS[1]: statsKey (device_stats:{supplier}:{ip})
-- KEYS[2]: sdkStatsKey (sdk_stats:{supplier}:{ip}:{sdkId})
-- ARGV[1]: supplier
-- ARGV[2]: sdkIP
-- ARGV[3]: sdkId
-- ARGV[4]: location
-- ARGV[5]: timestamp

local statsKey = KEYS[1]
local sdkStatsKey = KEYS[2]
local supplier = ARGV[1]
local sdkIP = ARGV[2]
local sdkId = ARGV[3]
local location = ARGV[4]
local timestamp = tonumber(ARGV[5])

-- 更新设备级统计
redis.call('HINCRBY', statsKey, 'currentSessions', 1)
redis.call('HSET', statsKey, 'lastConnected', timestamp)
redis.call('HSET', statsKey, 'supplier', supplier)
redis.call('HSET', statsKey, 'sdkIP', sdkIP)
redis.call('HSET', statsKey, 'location', location)

-- 更新SDK级统计
redis.call('HINCRBY', sdkStatsKey, 'currentSessions', 1)
redis.call('HSET', sdkStatsKey, 'lastConnected', timestamp)
redis.call('HSET', sdkStatsKey, 'supplier', supplier)
redis.call('HSET', sdkStatsKey, 'sdkIP', sdkIP)
redis.call('HSET', sdkStatsKey, 'sdkId', sdkId)
redis.call('HSET', sdkStatsKey, 'location', location)

return "OK"
`
	
	_, err := r.data.rdb.Eval(ctx, script,
		[]string{statsKey, sdkStatsKey},
		event.Supplier, event.SdkIp, event.SdkId, "", timestamp).Result()
	
	return err
}

// recordDisconnectWithLua 使用Lua脚本记录断连事件
func (r *onlineTimeRepo) recordDisconnectWithLua(ctx context.Context, statsKey, sdkStatsKey string, event *types.UserEvent, timestamp int64) error {
	const script = `
-- 参数说明:
-- KEYS[1]: statsKey (device_stats:{supplier}:{ip})
-- KEYS[2]: sdkStatsKey (sdk_stats:{supplier}:{ip}:{sdkId})
-- ARGV[1]: supplier
-- ARGV[2]: sdkIP
-- ARGV[3]: sdkId
-- ARGV[4]: timestamp

local statsKey = KEYS[1]
local sdkStatsKey = KEYS[2]
local supplier = ARGV[1]
local sdkIP = ARGV[2]
local sdkId = ARGV[3]
local timestamp = tonumber(ARGV[4])

-- 获取连接时间来计算在线时长
local lastConnected = redis.call('HGET', statsKey, 'lastConnected')
local onlineTime = 0
if lastConnected then
    onlineTime = timestamp - tonumber(lastConnected)
    if onlineTime < 0 then onlineTime = 0 end
end

-- 更新设备级统计
local currentSessions = tonumber(redis.call('HGET', statsKey, 'currentSessions') or 0)
if currentSessions > 0 then
    redis.call('HINCRBY', statsKey, 'currentSessions', -1)
end
redis.call('HINCRBY', statsKey, 'totalSessions', 1)
redis.call('HINCRBY', statsKey, 'totalOnlineTime', onlineTime)
redis.call('HSET', statsKey, 'lastDisconnected', timestamp)

-- 计算平均在线时间
local totalSessions = tonumber(redis.call('HGET', statsKey, 'totalSessions'))
local totalOnlineTime = tonumber(redis.call('HGET', statsKey, 'totalOnlineTime'))
if totalSessions > 0 then
    local avgOnlineTime = math.floor(totalOnlineTime / totalSessions)
    redis.call('HSET', statsKey, 'avgOnlineTime', avgOnlineTime)
end

-- 更新SDK级统计
local sdkCurrentSessions = tonumber(redis.call('HGET', sdkStatsKey, 'currentSessions') or 0)
if sdkCurrentSessions > 0 then
    redis.call('HINCRBY', sdkStatsKey, 'currentSessions', -1)
end
redis.call('HINCRBY', sdkStatsKey, 'totalSessions', 1)
redis.call('HINCRBY', sdkStatsKey, 'totalOnlineTime', onlineTime)
redis.call('HSET', sdkStatsKey, 'lastDisconnected', timestamp)

-- 计算SDK平均在线时间
local sdkTotalSessions = tonumber(redis.call('HGET', sdkStatsKey, 'totalSessions'))
local sdkTotalOnlineTime = tonumber(redis.call('HGET', sdkStatsKey, 'totalOnlineTime'))
if sdkTotalSessions > 0 then
    local sdkAvgOnlineTime = math.floor(sdkTotalOnlineTime / sdkTotalSessions)
    redis.call('HSET', sdkStatsKey, 'avgOnlineTime', sdkAvgOnlineTime)
end

return onlineTime
`
	
	_, err := r.data.rdb.Eval(ctx, script,
		[]string{statsKey, sdkStatsKey},
		event.Supplier, event.SdkIp, event.SdkId, timestamp).Result()
	
	return err
}

// parseOnlineStats 解析在线统计数据
func (r *onlineTimeRepo) parseOnlineStats(data map[string]string, sdkIP, supplier, sdkId string) (*OnlineTimeStats, error) {
	stats := &OnlineTimeStats{
		SdkId:    sdkId,
		SdkIP:    sdkIP,
		Supplier: supplier,
	}
	
	if location, ok := data["location"]; ok {
		stats.Location = location
	}
	
	if val, ok := data["totalSessions"]; ok {
		if parsed, err := strconv.ParseInt(val, 10, 64); err == nil {
			stats.TotalSessions = parsed
		}
	}
	
	if val, ok := data["totalOnlineTime"]; ok {
		if parsed, err := strconv.ParseInt(val, 10, 64); err == nil {
			stats.TotalOnlineTime = parsed
		}
	}
	
	if val, ok := data["avgOnlineTime"]; ok {
		if parsed, err := strconv.ParseInt(val, 10, 64); err == nil {
			stats.AvgOnlineTime = parsed
		}
	}
	
	if val, ok := data["lastConnected"]; ok {
		if parsed, err := strconv.ParseInt(val, 10, 64); err == nil {
			stats.LastConnected = parsed
		}
	}
	
	if val, ok := data["lastDisconnected"]; ok {
		if parsed, err := strconv.ParseInt(val, 10, 64); err == nil {
			stats.LastDisconnected = parsed
		}
	}
	
	if val, ok := data["currentSessions"]; ok {
		if parsed, err := strconv.ParseInt(val, 10, 64); err == nil {
			stats.CurrentSessions = parsed
		}
	}
	
	return stats, nil
}

// getDeviceStatsKey 获取设备统计键
func (r *onlineTimeRepo) getDeviceStatsKey(supplier, sdkIP string) string {
	return fmt.Sprintf("device_stats:%s:%s", supplier, sdkIP)
}

// getSDKStatsKey 获取SDK统计键
func (r *onlineTimeRepo) getSDKStatsKey(supplier, sdkIP, sdkId string) string {
	return fmt.Sprintf("sdk_stats:%s:%s:%s", supplier, sdkIP, sdkId)
}

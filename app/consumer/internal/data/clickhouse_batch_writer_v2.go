package data

import (
	"context"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gorm.io/gorm"

	"sh_proxy/app/consumer/internal/data/models"
	contextlog "sh_proxy/pkg/log"
)

// ClickHouseBatchWriterV2 改进的ClickHouse批量写入器
type ClickHouseBatchWriterV2 struct {
	log    *contextlog.Helper
	db     *gorm.DB
	config *BatchWriterConfig

	// 批量缓冲区
	buffer    []*models.SdkIpinfo
	bufferMux sync.Mutex

	// 控制通道
	flushTicker *time.Ticker
	stopCh      chan struct{}
	doneCh      chan struct{}

	// 统计信息
	totalRecords  int64
	totalBatches  int64
	lastFlushTime time.Time

	// 优雅关闭
	shutdownOnce sync.Once
}

// BatchWriterConfig 批量写入配置
type BatchWriterConfig struct {
	BatchSize     int           `yaml:"batch_size"`     // 批量大小，默认10000
	FlushInterval time.Duration `yaml:"flush_interval"` // 刷新间隔，默认1分钟
	MaxRetries    int           `yaml:"max_retries"`    // 最大重试次数
}

// NewClickHouseBatchWriterV2 创建改进的ClickHouse批量写入器
func NewClickHouseBatchWriterV2(logger log.Logger, db *gorm.DB) *ClickHouseBatchWriterV2 {
	// 设置默认配置
	config := &BatchWriterConfig{}
	if config.BatchSize <= 0 {
		config.BatchSize = 10000
	}
	if config.FlushInterval <= 0 {
		config.FlushInterval = time.Minute
	}
	if config.MaxRetries <= 0 {
		config.MaxRetries = 1
	}

	writer := &ClickHouseBatchWriterV2{
		log:           contextlog.NewHelper(log.With(logger, "module", "clickhouse_batch_writer_v2")),
		db:            db,
		config:        config,
		buffer:        make([]*models.SdkIpinfo, 0, config.BatchSize),
		stopCh:        make(chan struct{}),
		doneCh:        make(chan struct{}),
		lastFlushTime: time.Now(),
	}

	return writer
}

// Start 启动批量写入器
func (w *ClickHouseBatchWriterV2) Start() {
	w.flushTicker = time.NewTicker(w.config.FlushInterval)
	go w.flushWorker()

	w.log.Infow("msg", "ClickHouse批量写入器V2已启动",
		"batch_size", w.config.BatchSize,
		"flush_interval", w.config.FlushInterval,
	)
}

// Stop 停止批量写入器并刷新所有数据
func (w *ClickHouseBatchWriterV2) Stop(ctx context.Context) error {
	var stopErr error

	w.shutdownOnce.Do(func() {
		w.log.Info("正在停止ClickHouse批量写入器V2...")

		// 停止定时器
		if w.flushTicker != nil {
			w.flushTicker.Stop()
		}

		// 发送停止信号
		close(w.stopCh)

		// 等待工作协程完成或超时
		select {
		case <-w.doneCh:
			w.log.Info("ClickHouse批量写入器V2已正常停止")
		case <-ctx.Done():
			w.log.Warn("ClickHouse批量写入器V2停止超时")
			stopErr = ctx.Err()
		}

		// 最后一次刷新剩余数据
		if err := w.flushBuffer(context.Background()); err != nil {
			w.log.Errorw("msg", "最终刷新失败", "error", err)
			if stopErr == nil {
				stopErr = err
			}
		}

		w.log.Infow("msg", "ClickHouse批量写入器V2已完全停止",
			"total_records", w.totalRecords,
			"total_batches", w.totalBatches,
		)
	})

	return stopErr
}

// Add 添加记录到缓冲区
func (w *ClickHouseBatchWriterV2) Add(record *models.SdkIpinfo) {
	w.bufferMux.Lock()
	defer w.bufferMux.Unlock()

	w.buffer = append(w.buffer, record)

	// 检查是否需要立即刷新
	if len(w.buffer) >= w.config.BatchSize {
		// 异步触发刷新，避免阻塞
		go func() {
			if err := w.flushBuffer(context.Background()); err != nil {
				w.log.Errorw("msg", "异步刷新失败", "error", err)
			}
		}()
	}
}

// flushWorker 刷新工作协程
func (w *ClickHouseBatchWriterV2) flushWorker() {
	defer close(w.doneCh)

	for {
		select {
		case <-w.stopCh:
			// 停止信号，退出前最后刷新一次
			w.log.Info("收到停止信号，执行最后刷新")
			if err := w.flushBuffer(context.Background()); err != nil {
				w.log.Errorw("msg", "最后刷新失败", "error", err)
			}
			return

		case <-w.flushTicker.C:
			// 定时刷新
			if err := w.flushBuffer(context.Background()); err != nil {
				w.log.Errorw("msg", "定时刷新失败", "error", err)
			}
		}
	}
}

// flushBuffer 刷新缓冲区到ClickHouse
func (w *ClickHouseBatchWriterV2) flushBuffer(ctx context.Context) error {
	w.bufferMux.Lock()

	if len(w.buffer) == 0 {
		w.bufferMux.Unlock()
		return nil
	}

	// 复制缓冲区数据
	records := make([]*models.SdkIpinfo, len(w.buffer))
	copy(records, w.buffer)

	// 清空缓冲区
	w.buffer = w.buffer[:0]
	w.bufferMux.Unlock()

	// 执行批量插入
	return w.batchInsert(ctx, records)
}

// batchInsert 批量插入数据
func (w *ClickHouseBatchWriterV2) batchInsert(ctx context.Context, records []*models.SdkIpinfo) error {
	if len(records) == 0 {
		return nil
	}

	start := time.Now()

	// 重试逻辑
	var lastErr error
	for attempt := 0; attempt < w.config.MaxRetries; attempt++ {
		if attempt > 0 {
			w.log.Warnw("msg", "重试批量插入",
				"attempt", attempt+1,
				"records", len(records),
				"error", lastErr,
			)
			time.Sleep(time.Duration(attempt) * time.Second)
		}

		// 使用GORM批量插入
		if err := w.db.WithContext(ctx).CreateInBatches(records, len(records)).Error; err != nil {
			lastErr = err
			continue
		}

		// 插入成功
		w.totalRecords += int64(len(records))
		w.totalBatches++
		w.lastFlushTime = time.Now()

		w.log.Infow("msg", "批量插入成功",
			"records", len(records),
			"duration", time.Since(start),
			"total_records", w.totalRecords,
			"total_batches", w.totalBatches,
		)

		return nil
	}

	w.log.Errorw("msg", "批量插入最终失败",
		"records", len(records),
		"attempts", w.config.MaxRetries,
		"error", lastErr,
	)

	return lastErr
}

// GetStats 获取统计信息
func (w *ClickHouseBatchWriterV2) GetStats() map[string]interface{} {
	w.bufferMux.Lock()
	bufferSize := len(w.buffer)
	w.bufferMux.Unlock()

	return map[string]interface{}{
		"total_records":    w.totalRecords,
		"total_batches":    w.totalBatches,
		"buffer_size":      bufferSize,
		"last_flush_time":  w.lastFlushTime.Format(time.DateTime),
		"batch_size_limit": w.config.BatchSize,
		"flush_interval":   w.config.FlushInterval.String(),
	}
}

// ForceFlush 强制刷新缓冲区
func (w *ClickHouseBatchWriterV2) ForceFlush(ctx context.Context) error {
	return w.flushBuffer(ctx)
}

// GetBufferSize 获取当前缓冲区大小
func (w *ClickHouseBatchWriterV2) GetBufferSize() int {
	w.bufferMux.Lock()
	defer w.bufferMux.Unlock()
	return len(w.buffer)
}

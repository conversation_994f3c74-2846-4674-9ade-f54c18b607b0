package data

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"

	"sh_proxy/pkg/types"
)

type ConnInfo = types.ConnInfo

// HashConnInfo Hash存储的连接信息
type HashConnInfo struct {
	SdkId     string `json:"sdkId"`
	Host      string `json:"host"`
	ConnId    string `json:"connId"`
	CtlConnId string `json:"ctlConnId"`
	Location  string `json:"location"`
	Timestamp int64  `json:"timestamp"`
}

// ToJSON 转换为JSON字符串
func (h *HashConnInfo) ToJSON() string {
	data, _ := json.Marshal(h)
	return string(data)
}

// FromJSON 从JSON字符串解析
func (h *HashConnInfo) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), h)
}

// ToConnInfo 转换为ConnInfo
func (h *HashConnInfo) ToConnInfo() *ConnInfo {
	return &ConnInfo{
		Host:      h.Host,
		ConnId:    h.ConnId,
		Timestamp: h.Timestamp,
		CtlConnId: h.CtlConnId,
	}
}

// NewHashConnInfo 从ConnInfo创建HashConnInfo
func NewHashConnInfo(sdkId, location string, connInfo *ConnInfo) *HashConnInfo {
	return &HashConnInfo{
		SdkId:     sdkId,
		Host:      connInfo.Host,
		ConnId:    connInfo.ConnId,
		CtlConnId: connInfo.CtlConnId,
		Location:  location,
		Timestamp: connInfo.Timestamp,
	}
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	SdkIP     string                       `json:"sdkIp"`
	Supplier  string                       `json:"supplier"`
	Location  string                       `json:"location"`
	SdkConn   map[string][]*types.ConnInfo `json:"sdkConn"` // key 为 sdkId, value 为连接信息
	LastSeen  time.Time                    `json:"lastSeen"`
	CreatedAt time.Time                    `json:"createdAt"`
}

// DeviceMeta 设备元信息
type DeviceMeta struct {
	SdkIP     string `json:"sdkIp"`
	Supplier  string `json:"supplier"`
	Location  string `json:"location"`
	LastSeen  int64  `json:"lastSeen"`
	CreatedAt int64  `json:"createdAt"`
}

// ToJSON 转换为JSON字符串
func (d *DeviceMeta) ToJSON() string {
	data, _ := json.Marshal(d)
	return string(data)
}

// FromJSON 从JSON字符串解析
func (d *DeviceMeta) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), d)
}

// GetDeviceKey 获取设备的Redis key
func (d *DeviceInfo) GetDeviceKey() string {
	return fmt.Sprintf("device:{%s}:%s", d.Supplier, d.SdkIP)
}

// GetDeviceMetaKey 获取设备元信息的Redis key
func GetDeviceMetaKey(supplier, sdkIP string) string {
	return fmt.Sprintf("device:{%s}:%s:meta", supplier, sdkIP)
}

// GetHashFieldKey 获取Hash字段key
func GetHashFieldKey(ctlConnId, connId string) string {
	// 去除 uuid 的中线
	ctlConnId = strings.ReplaceAll(ctlConnId, "-", "")
	connId = strings.ReplaceAll(connId, "-", "")
	if len(ctlConnId) >= 16 {
		ctlConnId = ctlConnId[:16]
	}
	if len(connId) >= 16 {
		connId = connId[:16]
	}

	return fmt.Sprintf("%s:%s", ctlConnId, connId)
}

// GetLocationKey 获取位置的Redis key
func GetLocationKey(supplier, location string) string {
	return fmt.Sprintf("location:{%s}:%s", supplier, location)
}

// GetSessionKey 获取会话的Redis key
func GetSessionKey(authUser, session string) string {
	return fmt.Sprintf("session:%s:%s", authUser, session)
}

// LocationStats 位置统计信息
// type LocationStats struct {
// 	Supplier    string `json:"supplier"`
// 	Location    string `json:"location"`
// 	DeviceCount int64  `json:"deviceCount"`
// 	Weight      int64  `json:"weight"`
// }

// WeightedLocation 加权位置信息
type WeightedLocation struct {
	Supplier string `json:"supplier"`
	Location string `json:"location"`
	Weight   int64  `json:"weight"`
}

// DeviceRepo 设备仓库接口
type DeviceRepo interface {
	// 连接管理
	AddDevice(ctx context.Context, device *DeviceInfo, sdkID string, connInfo *ConnInfo) error
	RemoveDevice(ctx context.Context, supplier, sdkID, sdkIP, connID, ctlConnId string) error
	GetDevice(ctx context.Context, supplier, sdkIP string) (*DeviceInfo, error)
	SetCtlUnavailable(ctx context.Context, ctlConnId string) error
	RemoveConnectionsByCtlConnId(ctx context.Context, ctlConnId string) error
	RemoveConnectionsByCtlConnIdV2(ctx context.Context, ctlConnId string) error
	RemoveConnectionsByCtlConnIdHash(ctx context.Context, ctlConnId string) error
	IsCtlUnavailable(ctx context.Context, ctlConnId string) (bool, error)

	// 位置查询
	GetDevicesByLocation(ctx context.Context, supplier, location string) ([]*DeviceInfo, error)
	GetRandomDeviceForMixedMode(ctx context.Context, supplier string) (*DeviceInfo, error)

	// 位置统计和管理
	GetLocationDeviceCount(ctx context.Context, supplier, location string) (int64, error)
	UpdateLocationStats(ctx context.Context, supplier, location string, deviceCount int64) error

	// 混播模式权重管理
	UpdateLocationWeight(ctx context.Context, supplier, location string, weight int64) error
	GetRandomLocationByWeight(ctx context.Context, supplier string) (string, error)

	// 会话管理
	SetSessionDevice(ctx context.Context, authUser, session, sdkID, sdkIP string, ttl time.Duration) error
	GetSessionDevice(ctx context.Context, authUser, session string) (string, string, error)
}

type deviceRepo struct {
	data        *Data
	lockManager *LockManager
	lms         *LocationMappingService
}

// NewDeviceRepo 创建设备仓库
func NewDeviceRepo(data *Data, lms *LocationMappingService) DeviceRepo {
	return &deviceRepo{
		data:        data,
		lockManager: data.lockManager,
		lms:         lms,
	}
}

// AddDevice 添加设备连接（使用Hash存储，无需锁）
func (r *deviceRepo) AddDevice(ctx context.Context, device *DeviceInfo, sdkID string, connInfo *ConnInfo) error {
	return r.addDeviceWithHash(ctx, device, sdkID, connInfo)
}

// addDeviceWithHash 使用Hash存储添加设备连接
func (r *deviceRepo) addDeviceWithHash(ctx context.Context, device *DeviceInfo, sdkID string, connInfo *ConnInfo) error {
	deviceKey := device.GetDeviceKey()
	fieldKey := GetHashFieldKey(connInfo.CtlConnId, connInfo.ConnId)
	locationKey := GetLocationKey(device.Supplier, device.Location)

	// 创建Hash连接信息
	hashConn := NewHashConnInfo(sdkID, device.Location, connInfo)

	// 使用Lua脚本原子性地执行exists检查和hash操作
	isNewDevice, err := r.addDeviceWithLua(ctx, deviceKey, locationKey, fieldKey,
		hashConn.ToJSON(), device.SdkIP)
	if err != nil {
		return fmt.Errorf("failed to execute lua script: %w", err)
	}

	// 如果是新设备，更新权重和位置映射，并异步写入ClickHouse
	if isNewDevice {
		pipe := r.data.rdb.TxPipeline()
		r.updateMixedModeWeight(ctx, pipe, device.Supplier, device.Location, 1)
		r.lms.AddDeviceLocationMapping(ctx, pipe, device.Supplier, device.Location)

		_, err = pipe.Exec(ctx)
		if err != nil {
			r.data.log.Warnf("Failed to update weight and location mapping: %v", err)
		}

		// 异步写入ClickHouse
		r.addToClickHouse(device, sdkID, connInfo)
	}

	r.data.log.Infof("Device connection added using hash: %s:%s, fieldKey: %s, location: %s, isNew: %v",
		device.SdkIP, sdkID, fieldKey, device.Location, isNewDevice)

	return nil
}

// addDeviceWithLua 使用Lua脚本原子性地添加设备连接
func (r *deviceRepo) addDeviceWithLua(ctx context.Context, deviceKey, locationKey, fieldKey,
	connJSON, sdkIP string) (bool, error) {

	const script = `
-- 参数说明:
-- KEYS[1]: deviceKey (device:{supplier}:{ip})
-- KEYS[2]: locationKey (location:{supplier}:{location})
-- ARGV[1]: fieldKey ({ctlConnId}:{sdkId}:{connId})
-- ARGV[2]: connJSON (连接信息JSON)
-- ARGV[3]: sdkIP (ip)

local deviceKey = KEYS[1]
local locationKey = KEYS[2]
local fieldKey = ARGV[1]
local connJSON = ARGV[2]
local sdkIP = ARGV[3]

-- 检查设备元信息是否存在
local deviceExists = redis.call('EXISTS', deviceKey)
local isNewDevice = (deviceExists == 0)

-- 1. 添加连接到Hash
redis.call('HSET', deviceKey, fieldKey, connJSON)


-- 3. 添加到位置集合
redis.call('SADD', locationKey, sdkIP)

-- 返回是否为新设备 (1表示新设备，0表示已存在)
return isNewDevice and 1 or 0
`

	result, err := r.data.rdb.Eval(ctx, script,
		[]string{deviceKey, locationKey},
		fieldKey, connJSON, sdkIP).Result()

	if err != nil {
		return false, err
	}

	return result.(int64) == 1, nil
}

// addDeviceWithoutLock 不使用锁的添加设备方法（内部使用）
func (r *deviceRepo) addDeviceWithoutLock(ctx context.Context, device *DeviceInfo, sdkID string, connInfo *ConnInfo) error {
	deviceKey := device.GetDeviceKey()
	locationKey := GetLocationKey(device.Supplier, device.Location)

	// 获取现有设备信息
	existingDevice, _ := r.GetDevice(ctx, device.Supplier, device.SdkIP)
	if existingDevice != nil {
		device.SdkConn = existingDevice.SdkConn
		device.CreatedAt = existingDevice.CreatedAt
	} else {
		device.CreatedAt = time.Now()
	}
	// 不存在连接信息，则初始化
	if device.SdkConn == nil {
		device.SdkConn = make(map[string][]*ConnInfo)
	}

	// 检查连接是否已存在
	if _, exists := device.SdkConn[sdkID]; exists {
		for _, conn := range device.SdkConn[sdkID] {
			if conn.ConnId == connInfo.ConnId {
				r.data.log.Warnf("Connection ID already exists: %s:%s, connID: %s",
					device.SdkIP, sdkID, connInfo.ConnId)
				return nil // 连接已存在，不需要重复添加
			}
		}
		device.SdkConn[sdkID] = append(device.SdkConn[sdkID], connInfo)
	} else {
		device.SdkConn[sdkID] = []*ConnInfo{connInfo}
	}

	device.LastSeen = time.Now()

	// 使用事务确保原子性
	pipe := r.data.rdb.TxPipeline()

	// 序列化设备信息
	deviceData, err := json.Marshal(device)
	if err != nil {
		return fmt.Errorf("failed to marshal device info: %w", err)
	}

	// 存储设备信息
	pipe.Set(ctx, deviceKey, deviceData, redis.KeepTTL)

	// 添加到位置集合
	pipe.SAdd(ctx, locationKey, device.SdkIP)

	// 更新位置统计信息
	// statsKey := fmt.Sprintf("location_stats:%s:%s", device.Supplier, device.Location)
	// pipe.Incr(ctx, statsKey)
	// pipe.Expire(ctx, statsKey, 24*time.Hour)

	// 新增 ip 时，增加权重
	if existingDevice == nil {
		// 更新混播权重索引
		r.updateMixedModeWeight(ctx, pipe, device.Supplier, device.Location, 1)
		// 更新对应 location 权重
		r.lms.AddDeviceLocationMapping(ctx, pipe, device.Supplier, device.Location)
	}

	// 执行事务
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute redis transaction: %w", err)
	}

	r.data.log.Infof("Device connection added: %s:%s, connID: %s, location: %s, total_connections: %d",
		device.SdkIP, sdkID, connInfo.ConnId, device.Location, len(device.SdkConn[sdkID]))

	return nil
}

// RemoveDevice 移除设备连接（使用Hash存储，无需锁）
func (r *deviceRepo) RemoveDevice(ctx context.Context, supplier, sdkID, sdkIP, connID, ctlConnId string) error {
	return r.removeDeviceWithHash(ctx, supplier, sdkID, sdkIP, connID, ctlConnId)
}

// removeDeviceWithHash 使用Hash存储移除设备连接
func (r *deviceRepo) removeDeviceWithHash(ctx context.Context, supplier, sdkID, sdkIP, connID, ctlConnId string) error {
	deviceKey := fmt.Sprintf("device:{%s}:%s", supplier, sdkIP)

	// 随机获取一个 field
	hashConnInfo, err := r.getRandHashConn(ctx, deviceKey)
	if err != nil {
		r.data.log.WithContext(ctx).Warnw("msg", "Failed to get random hash conn", "device_key", deviceKey, "error", err)
		return nil
	}

	// 从位置集合中移除
	locationKey := GetLocationKey(supplier, hashConnInfo.Location)

	// 使用Lua脚本原子性地移除连接并检查是否需要清理
	isEmpty, err := r.removeDeviceWithLua(ctx, deviceKey, locationKey, GetHashFieldKey(ctlConnId, connID), sdkIP)
	if err != nil {
		return fmt.Errorf("failed to execute lua script: %w", err)
	}

	// 如果设备为空，清理相关数据
	if isEmpty {
		pipe := r.data.rdb.TxPipeline()
		r.cleanupEmptyDevice(ctx, pipe, supplier, sdkIP, hashConnInfo.Location)

		_, err = pipe.Exec(ctx)
		if err != nil {
			r.data.log.Warnf("Failed to cleanup empty device: %v", err)
		}
	}

	r.data.log.Infof("Device connection removed using hash: %s:%s, deviceKey: %s, isEmpty: %v",
		ctlConnId, sdkID, deviceKey, isEmpty)

	return nil
}

func (r *deviceRepo) getRandHashConn(ctx context.Context, deviceKey string) (*HashConnInfo, error) {
	// 随机获取一个 field
	result := r.data.rdb.HRandFieldWithValues(ctx, deviceKey, 1)
	if result.Err() != nil {
		return nil, result.Err()
	}
	if len(result.Val()) == 0 {
		return nil, errors.New("device is empty")
	}
	fieldVal := result.Val()[0]
	var hashConnInfo HashConnInfo
	if err := json.Unmarshal([]byte(fieldVal.Value), &hashConnInfo); err != nil {
		return nil, errors.Wrapf(err, "failed to unmarshal hash conn info: %s", fieldVal.Value)
	}
	return &hashConnInfo, nil
}

// removeDeviceWithLua 使用Lua脚本原子性地移除设备连接
func (r *deviceRepo) removeDeviceWithLua(ctx context.Context, deviceKey, locationKey, fieldToRemove, sdkIP string) (bool, error) {
	const script = `
-- 参数说明:
-- KEYS[1]: deviceKey (device:{supplier}:{ip})
-- KEYS[2]: locationKey (location:{supplier}:{location})
-- ARGV[1]: fieldToRemove
-- ARGV[2]: sdkIP (ip)

local deviceKey = KEYS[1]
local locationKey = KEYS[2]
local fieldToRemove = ARGV[1]
local sdkIP = ARGV[2]


-- 移除连接
redis.call('HDEL', deviceKey, fieldToRemove)

-- 检查是否还有其他连接
local remainingCount = redis.call('HLEN', deviceKey)

local isEmpty = (remainingCount == 0)

-- 删除该 key
if remainingCount == 0 then 
	redis.call('DEL', deviceKey)
	-- 从位置集合中移除
	redis.call('SREM', locationKey, sdkIP)
end

-- 返回结果:
return isEmpty and 1 or 0
`

	result, err := r.data.rdb.Eval(ctx, script,
		[]string{deviceKey, locationKey},
		fieldToRemove,
		sdkIP).Result()

	if err != nil {
		return false, err
	}

	return result.(int64) == 1, nil
}

// cleanupEmptyDevice 清理空设备
func (r *deviceRepo) cleanupEmptyDevice(ctx context.Context, pipe redis.Pipeliner, supplier, sdkIP, location string) {

	// 更新混播权重索引
	r.updateMixedModeWeight(ctx, pipe, supplier, location, -1)

	// 更新位置映射
	r.lms.RemoveDeviceLocationMapping(ctx, pipe, supplier, location)
}

// removeDeviceWithoutLock 不使用锁的移除设备方法（内部使用）
func (r *deviceRepo) removeDeviceWithoutLock(ctx context.Context, supplier, sdkID, sdkIP, connID string) error {
	// 获取现有设备信息
	device, err := r.GetDevice(ctx, supplier, sdkIP)
	if err != nil || device == nil {
		r.data.log.WithContext(ctx).Warnf("Device not found for removal: %s:%s", sdkID, sdkIP)
		return nil // 设备不存在，认为已经移除
	}

	if device.SdkConn == nil || len(device.SdkConn) == 0 {
		r.data.log.WithContext(ctx).Warnf("Device no sdk for removal: %s:%s", sdkID, sdkIP)
		return nil // 设备不存在，认为已经移除
	}
	if _, exists := device.SdkConn[sdkID]; !exists {
		r.data.log.WithContext(ctx).Warnf("Device sdk not found for removal: %s:%s", sdkID, sdkIP)
		return nil // 设备不存在，认为已经移除
	}

	var (
		existsConns = device.SdkConn[sdkID]
		newConns    []*ConnInfo
	)
	for i, conn := range existsConns {
		if conn.ConnId != connID {
			newConns = append(newConns, existsConns[i])
		}
	}
	if len(existsConns) == len(newConns) {
		r.data.log.WithContext(ctx).Warnf("Device sdk conn not found for removal: %s:%s, connID: %s", sdkID, sdkIP, connID)
		return nil // 设备不存在，认为已经移除
	}

	device.SdkConn[sdkID] = newConns
	device.LastSeen = time.Now()
	if len(device.SdkConn[sdkID]) == 0 {
		delete(device.SdkConn, sdkID)
	}

	if err := r.removeDeviceFromRedis(ctx, supplier, sdkIP, device); err != nil {
		return err
	}
	r.data.log.WithContext(ctx).Infof("Device connection removed: %s:%s, connID: %s, remaining connections: %d",
		sdkID, sdkIP, connID, len(device.SdkConn[sdkID]))

	return nil
}

func (r *deviceRepo) removeDeviceFromRedis(ctx context.Context, supplier, sdkIP string, device *DeviceInfo) error {
	if supplier == "" {
		supplier = device.Supplier
	}
	if sdkIP == "" {
		sdkIP = device.SdkIP
	}
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, sdkIP)

	pipe := r.data.rdb.TxPipeline()

	// 如果还有其他连接，更新设备信息
	if len(device.SdkConn) > 0 {
		deviceData, err := json.Marshal(device)
		if err != nil {
			return fmt.Errorf("failed to marshal device info: %w", err)
		}
		pipe.Set(ctx, deviceKey, deviceData, redis.KeepTTL)
	} else {
		// 没有连接了，完全移除设备
		pipe.Del(ctx, deviceKey)

		// 从位置集合中移除
		locationKey := GetLocationKey(supplier, device.Location)
		pipe.SRem(ctx, locationKey, sdkIP)

		// 更新位置统计信息
		// statsKey := fmt.Sprintf("location_stats:%s:%s", device.Supplier, device.Location)
		// pipe.Decr(ctx, statsKey)

		// 更新混播权重索引
		r.updateMixedModeWeight(ctx, pipe, device.Supplier, device.Location, -1)
		// 更新对应 location 权重
		r.lms.RemoveDeviceLocationMapping(ctx, pipe, device.Supplier, device.Location)
	}

	// 执行事务
	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute redis transaction: %w", err)
	}

	return nil
}

// GetDevice 获取设备信息（支持Hash和JSON格式）
func (r *deviceRepo) GetDevice(ctx context.Context, supplier, sdkIP string) (*DeviceInfo, error) {
	// 先尝试从Hash格式读取
	if device, err := r.getDeviceFromHash(ctx, supplier, sdkIP); err == nil && device != nil {
		return device, nil
	}

	// 降级到旧的JSON格式
	return r.getDeviceFromJSON(ctx, supplier, sdkIP)
}

// getDeviceFromHash 从Hash格式获取设备信息
func (r *deviceRepo) getDeviceFromHash(ctx context.Context, supplier, sdkIP string) (*DeviceInfo, error) {
	deviceKey := fmt.Sprintf("device:{%s}:%s", supplier, sdkIP)
	metaKey := GetDeviceMetaKey(supplier, sdkIP)

	// 1. 获取所有连接信息
	connMap, err := r.data.rdb.HGetAll(ctx, deviceKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get device connections: %w", err)
	}

	if len(connMap) == 0 {
		return nil, nil
	}

	// 2. 获取设备元信息
	metaMap, err := r.data.rdb.HGetAll(ctx, metaKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get device meta: %w", err)
	}

	// 3. 构建DeviceInfo
	device := &DeviceInfo{
		SdkIP:    sdkIP,
		Supplier: supplier,
		SdkConn:  make(map[string][]*types.ConnInfo),
	}

	// 解析元信息
	if location, ok := metaMap["location"]; ok {
		device.Location = location
	}
	if lastSeenStr, ok := metaMap["lastSeen"]; ok {
		if lastSeen, err := strconv.ParseInt(lastSeenStr, 10, 64); err == nil {
			device.LastSeen = time.Unix(lastSeen, 0)
		}
	}
	if createdAtStr, ok := metaMap["createdAt"]; ok {
		if createdAt, err := strconv.ParseInt(createdAtStr, 10, 64); err == nil {
			device.CreatedAt = time.Unix(createdAt, 0)
		}
	}

	// 4. 解析连接信息
	for fieldKey, connJSON := range connMap {
		var hashConn HashConnInfo
		if err := hashConn.FromJSON(connJSON); err != nil {
			r.data.log.WithContext(ctx).Warnf("Failed to parse connection info: %s, error: %v", fieldKey, err)
			continue
		}

		connInfo := hashConn.ToConnInfo()
		sdkId := hashConn.SdkId

		if device.SdkConn[sdkId] == nil {
			device.SdkConn[sdkId] = make([]*types.ConnInfo, 0)
		}
		device.SdkConn[sdkId] = append(device.SdkConn[sdkId], connInfo)
	}

	return device, nil
}

// getDeviceFromJSON 从JSON格式获取设备信息（兼容旧格式）
func (r *deviceRepo) getDeviceFromJSON(ctx context.Context, supplier, sdkIP string) (*DeviceInfo, error) {
	deviceKey := fmt.Sprintf("device:{%s}:%s", supplier, sdkIP)

	data, err := r.data.rdb.Get(ctx, deviceKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, err
	}

	var device DeviceInfo
	if err := json.Unmarshal([]byte(data), &device); err != nil {
		return nil, fmt.Errorf("failed to unmarshal device info: %w", err)
	}

	return &device, nil
}

// GetDevicesByLocation 根据位置获取设备列表
func (r *deviceRepo) GetDevicesByLocation(ctx context.Context, supplier, location string) ([]*DeviceInfo, error) {
	locationKey := GetLocationKey(supplier, location)

	sdkIPs, err := r.data.rdb.SMembers(ctx, locationKey).Result()
	if err != nil {
		return nil, err
	}

	var devices []*DeviceInfo
	for _, sdkIP := range sdkIPs {
		device, err := r.GetDevice(ctx, supplier, sdkIP)
		if err != nil || device == nil {
			// 设备不存在，从位置集合中清理
			r.data.rdb.SRem(ctx, locationKey, sdkIP)
			continue
		}

		// 检查设备是否有活跃连接
		if len(device.SdkConn) == 0 {
			// 没有活跃连接，从位置集合中清理
			r.data.rdb.SRem(ctx, locationKey, sdkIP)
			continue
		}

		devices = append(devices, device)
	}

	return devices, nil
}

func (r *deviceRepo) GetDeviceByLocation(ctx context.Context, supplier, location string) (*DeviceInfo, error) {
	locationKey := GetLocationKey(supplier, location)

	sdkIP, err := r.data.rdb.SRandMember(ctx, locationKey).Result()
	if err != nil {
		return nil, err
	}

	device, err := r.GetDevice(ctx, supplier, sdkIP)
	if err != nil || device == nil {
		// 设备不存在，从位置集合中清理
		r.data.rdb.SRem(ctx, locationKey, sdkIP)
		return nil, nil
	}

	// 检查设备是否有活跃连接
	if len(device.SdkConn) == 0 {
		// 没有活跃连接，从位置集合中清理
		r.data.rdb.SRem(ctx, locationKey, sdkIP)
		return nil, nil
	}

	return device, nil
}

// GetRandomDevicesFromLocations 从所有位置中随机获取设备（混播模式）
// func (r *deviceRepo) GetRandomDevicesFromLocations(ctx context.Context, supplier string, maxDevices int) ([]*DeviceInfo, error) {
// 	// 获取所有位置的统计信息
// 	locationStats, err := r.GetAllLocationStats(ctx)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to get location stats: %w", err)
// 	}
//
// 	if len(locationStats) == 0 {
// 		return []*DeviceInfo{}, nil
// 	}
//
// 	// 计算总权重
// 	var totalWeight int64
// 	for _, stat := range locationStats {
// 		totalWeight += stat.Weight
// 	}
//
// 	if totalWeight == 0 {
// 		return []*DeviceInfo{}, nil
// 	}
//
// 	var selectedDevices []*DeviceInfo
// 	selectedCount := 0
//
// 	// 基于权重随机选择位置，然后从该位置随机选择设备
// 	for selectedCount < maxDevices && len(locationStats) > 0 {
// 		// 加权随机选择位置
// 		selectedLocation := r.weightedRandomSelect(locationStats, totalWeight)
// 		if selectedLocation == nil {
// 			break
// 		}
//
// 		// 从选中的位置获取设备
// 		devices, err := r.GetDevicesByLocation(ctx, supplier, selectedLocation.Location)
// 		if err != nil || len(devices) == 0 {
// 			// 移除无效的位置统计
// 			r.removeLocationFromStats(locationStats, selectedLocation.Location)
// 			totalWeight -= selectedLocation.Weight
// 			continue
// 		}
//
// 		// 随机选择该位置的一个设备
// 		if len(devices) > 0 {
// 			randomIndex := rand.Intn(len(devices))
// 			selectedDevices = append(selectedDevices, devices[randomIndex])
// 			selectedCount++
// 		}
//
// 		// 减少该位置的权重，避免重复选择
// 		selectedLocation.Weight = selectedLocation.Weight / 2
// 		if selectedLocation.Weight <= 0 {
// 			r.removeLocationFromStats(locationStats, selectedLocation.Location)
// 		}
// 		totalWeight = r.recalculateTotalWeight(locationStats)
// 	}
//
// 	return selectedDevices, nil
// }

// GetAllLocationStats 获取所有位置的统计信息
// func (r *deviceRepo) GetAllLocationStats(ctx context.Context) ([]*LocationStats, error) {
// 	// 使用 SCAN 命令遍历所有位置统计键
// 	pattern := "location_stats:*"
// 	var cursor uint64
// 	var stats []*LocationStats
//
// 	for {
// 		keys, nextCursor, err := r.data.rdb.Scan(ctx, cursor, pattern, 100).Result()
// 		if err != nil {
// 			return nil, err
// 		}
//
// 		// 批量获取统计数据
// 		if len(keys) > 0 {
// 			pipe := r.data.rdb.Pipeline()
// 			cmds := make([]*redis.StringCmd, len(keys))
//
// 			for i, key := range keys {
// 				cmds[i] = pipe.Get(ctx, key)
// 			}
//
// 			_, err := pipe.Exec(ctx)
// 			if err != nil && err != redis.Nil {
// 				return nil, err
// 			}
//
// 			for i, cmd := range cmds {
// 				if cmd.Err() == nil {
// 					location := strings.TrimPrefix(keys[i], "location_stats:")
// 					if count, err := cmd.Int64(); err == nil && count > 0 {
// 						stats = append(stats, &LocationStats{
// 							Location:    location,
// 							DeviceCount: count,
// 							Weight:      count, // 权重等于设备数量
// 						})
// 					}
// 				}
// 			}
// 		}
//
// 		cursor = nextCursor
// 		if cursor == 0 {
// 			break
// 		}
// 	}
//
// 	return stats, nil
// }

// UpdateLocationStats 更新位置统计信息
func (r *deviceRepo) UpdateLocationStats(ctx context.Context, supplier, location string, deviceCount int64) error {
	statsKey := fmt.Sprintf("location_stats:%s:%s", supplier, location)
	return r.data.rdb.Set(ctx, statsKey, deviceCount, 24*time.Hour).Err()
}

// weightedRandomSelect 加权随机选择位置
// func (r *deviceRepo) weightedRandomSelect(stats []*LocationStats, totalWeight int64) *LocationStats {
// 	if totalWeight <= 0 {
// 		return nil
// 	}
//
// 	randomWeight := rand.Int63n(totalWeight)
// 	var currentWeight int64
//
// 	for _, stat := range stats {
// 		currentWeight += stat.Weight
// 		if randomWeight < currentWeight {
// 			return stat
// 		}
// 	}
//
// 	return nil
// }

// removeLocationFromStats 从统计列表中移除位置
// func (r *deviceRepo) removeLocationFromStats(stats []*LocationStats, location string) {
// 	for i, stat := range stats {
// 		if stat.Location == location {
// 			// 移除元素
// 			copy(stats[i:], stats[i+1:])
// 			stats = stats[:len(stats)-1]
// 			break
// 		}
// 	}
// }

// recalculateTotalWeight 重新计算总权重
// func (r *deviceRepo) recalculateTotalWeight(stats []*LocationStats) int64 {
// 	var total int64
// 	for _, stat := range stats {
// 		total += stat.Weight
// 	}
// 	return total
// }

// SetSessionDevice 设置会话绑定的设备
func (r *deviceRepo) SetSessionDevice(ctx context.Context, authUser, session, sdkID, sdkIP string, ttl time.Duration) error {
	sessionKey := GetSessionKey(authUser, session)
	deviceIdentifier := fmt.Sprintf("%s:%s", sdkID, sdkIP)

	return r.data.rdb.Set(ctx, sessionKey, deviceIdentifier, ttl).Err()
}

// GetSessionDevice 获取会话绑定的设备
func (r *deviceRepo) GetSessionDevice(ctx context.Context, authUser, session string) (string, string, error) {
	sessionKey := GetSessionKey(authUser, session)

	deviceIdentifier, err := r.data.rdb.Get(ctx, sessionKey).Result()
	if err != nil {
		if err == redis.Nil {
			return "", "", nil
		}
		return "", "", err
	}

	parts := strings.Split(deviceIdentifier, ":")
	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid device identifier: %s", deviceIdentifier)
	}

	return parts[0], parts[1], nil
}

// GetLocationDeviceCount 获取位置的设备数量
func (r *deviceRepo) GetLocationDeviceCount(ctx context.Context, supplier, location string) (int64, error) {
	locationKey := GetLocationKey(supplier, location)
	return r.data.rdb.SCard(ctx, locationKey).Result()
}

// GetRandomDeviceForMixedMode 混播模式：基于权重随机选择一个设备（高效版本）
func (r *deviceRepo) GetRandomDeviceForMixedMode(ctx context.Context, supplier string) (*DeviceInfo, error) {
	// 1. 基于权重随机选择一个位置
	selectedLocation, err := r.GetRandomLocationByWeight(ctx, supplier)
	if err != nil {
		return nil, fmt.Errorf("failed to select random location: %w", err)
	}

	if selectedLocation == "" {
		return nil, fmt.Errorf("no available locations for supplier: %s", supplier)
	}

	// 2. 从选中的位置随机选择一个设备
	selectedDevice, err := r.GetDeviceByLocation(ctx, supplier, selectedLocation)
	if err != nil {
		return nil, fmt.Errorf("failed to get device from location %s: %w", selectedLocation, err)
	}
	if selectedDevice == nil {
		return nil, fmt.Errorf("no devices available in location: %s", selectedLocation)
	}

	// 2. 从选中的位置随机选择一个设备
	// devices, err := r.GetDevicesByLocation(ctx, supplier, selectedLocation)
	// if err != nil {
	// 	return nil, fmt.Errorf("failed to get devices from location %s: %w", selectedLocation, err)
	// }
	//
	// if len(devices) == 0 {
	// 	// 位置没有设备，清理权重索引
	// 	r.cleanupLocationWeight(ctx, supplier, selectedLocation)
	// 	return nil, fmt.Errorf("no devices available in location: %s", selectedLocation)
	// }
	//
	// // 3. 随机选择一个设备
	// rand.Seed(time.Now().UnixNano())
	// selectedDevice := devices[rand.Intn(len(devices))]

	return selectedDevice, nil
}

// GetRandomLocationByWeight 基于权重随机选择位置
func (r *deviceRepo) GetRandomLocationByWeight(ctx context.Context, supplier string) (string, error) {
	// 使用 Redis 的 ZRANDMEMBER 命令基于权重随机选择
	// 权重索引键：mixed_weight:{supplier}
	weightKey := fmt.Sprintf("mixed_weight:{%s}", supplier)

	// 使用 ZRANDMEMBER 命令随机选择一个位置（基于分数权重）
	result, err := r.data.rdb.ZRandMemberWithScores(ctx, weightKey, 1).Result()
	if err != nil {
		if err == redis.Nil {
			return "", nil // 没有可用位置
		}
		return "", fmt.Errorf("failed to get random weighted location: %w", err)
	}

	if len(result) == 0 {
		return "", nil
	}

	return result[0].Member.(string), nil
}

// UpdateLocationWeight 更新位置权重
func (r *deviceRepo) UpdateLocationWeight(ctx context.Context, supplier, location string, weight int64) error {
	weightKey := fmt.Sprintf("mixed_weight:{%s}", supplier)

	if weight <= 0 {
		// 权重为0或负数，从权重索引中移除
		return r.data.rdb.ZRem(ctx, weightKey, location).Err()
	}

	// 更新权重
	return r.data.rdb.ZAdd(ctx, weightKey, redis.Z{
		Score:  float64(weight),
		Member: location,
	}).Err()
}

// updateMixedModeWeight 更新混播模式权重（内部方法）
func (r *deviceRepo) updateMixedModeWeight(ctx context.Context, pipe redis.Pipeliner, supplier, location string, delta int64) {
	weightKey := fmt.Sprintf("mixed_weight:{%s}", supplier)

	if delta > 0 {
		// 增加权重
		pipe.ZIncrBy(ctx, weightKey, float64(delta), location)
		// pipe.Expire(ctx, weightKey, redis.KeepTTL)
	} else {
		// 减少权重
		pipe.ZIncrBy(ctx, weightKey, float64(delta), location)
		// 如果权重降到0或以下，移除该位置
		pipe.ZRemRangeByScore(ctx, weightKey, "-inf", "0")
	}
}

// cleanupLocationWeight 清理无效的位置权重
func (r *deviceRepo) cleanupLocationWeight(ctx context.Context, supplier, location string) {
	weightKey := fmt.Sprintf("mixed_weight:{%s}", supplier)
	r.data.rdb.ZRem(ctx, weightKey, location)
}

// SetCtlUnavailable sets a ctlConnId as unavailable
func (r *deviceRepo) SetCtlUnavailable(ctx context.Context, ctlConnId string) error {
	unavailableKey := "ctlunavailable"

	if err := r.data.rdb.SAdd(ctx, unavailableKey, ctlConnId).Err(); err != nil {
		return errors.Wrap(err, "failed to add ctlunavailable")
	}
	return nil
}

// RemoveConnectionsByCtlConnId removes all connections associated with the given ctlConnId
func (r *deviceRepo) RemoveConnectionsByCtlConnId(ctx context.Context, ctlConnId string) error {
	var cursor uint64
	var keys []string
	var err error

	for {
		keys, cursor, err = r.data.rdb.Scan(ctx, cursor, "device:*", 100).Result()
		if err != nil {
			return errors.Wrap(err, "failed to scan device keys")
		}

		for _, deviceKey := range keys {
			lockKey := "lock:" + deviceKey
			lockTTL := 30 * time.Second

			// Use distributed lock for each device
			err = r.lockManager.WithLockRetry(ctx, lockKey, lockTTL, 3, 100*time.Millisecond, func() error {
				deviceData, err := r.data.rdb.Get(ctx, deviceKey).Result()
				if err != nil {
					if err == redis.Nil {
						return nil // Device already gone, skip
					}
					return err
				}

				var device DeviceInfo
				if err := json.Unmarshal([]byte(deviceData), &device); err != nil {
					r.data.log.WithContext(ctx).Errorf("Failed to unmarshal device info for key %s: %v", deviceKey, err)
					return nil // Skip this malformed device
				}

				changed := false
				newSdkConn := make(map[string][]*types.ConnInfo)
				for sdkID, connInfos := range device.SdkConn {
					var newConnInfos []*types.ConnInfo
					for _, conn := range connInfos {
						if conn.CtlConnId != ctlConnId { // Assuming ctlConnId directly maps to ConnInfo.CtlConnId
							newConnInfos = append(newConnInfos, conn)
						} else {
							changed = true
						}
					}
					if len(newConnInfos) > 0 {
						newSdkConn[sdkID] = newConnInfos
					} else {
						changed = true // All connections for this SDK removed
					}
				}

				if changed {
					device.SdkConn = newSdkConn
					device.LastSeen = time.Now()
					if err := r.removeDeviceFromRedis(ctx, device.Supplier, device.SdkIP, &device); err != nil {
						return err
					}
					r.data.log.WithContext(ctx).Infow("msg", "Removed connections for ctlConnId", "ctl_conn_id", ctlConnId, "device_key", deviceKey)
				}
				return nil
			})
			if err != nil {
				r.data.log.WithContext(ctx).Errorw("msg", "Failed to process device key for ctlConnId removal", "device_key", deviceKey, "error", err)
				// Continue to next device, but log the error
			}
		}

		if cursor == 0 {
			break
		}
	}
	r.RemoveCtlUnavailable(ctx, ctlConnId)
	return nil
}

func (r *deviceRepo) RemoveConnectionsByCtlConnIdHash(ctx context.Context, originCtlConnId string) error {
	var cursor uint64
	var keys []string
	var err error
	ctlConnId := strings.ReplaceAll(originCtlConnId, "-", "")
	if len(ctlConnId) >= 16 {
		ctlConnId = ctlConnId[:16]
	}

	for {
		keys, cursor, err = r.data.rdb.Scan(ctx, cursor, "device:*", 100).Result()
		if err != nil {
			return errors.Wrap(err, "failed to scan device keys")
		}

		for _, deviceKey := range keys {
			var ctlCursor uint64
			var fields []string
			var ctlErr error

			for {
				fields, ctlCursor, ctlErr = r.data.rdb.HScan(ctx, deviceKey, ctlCursor, ctlConnId+":*", 100).Result()
				if ctlErr != nil {
					return errors.Wrap(ctlErr, "failed to scan device fields")
				}
				supplier, sdkIP := r.getSupplierIpFromDeviceKey(deviceKey)
				for _, field := range fields {
					ctlConnIdItem, connID := r.getCtlAndConnFromField(field)
					// Remove the connection
					if err := r.removeDeviceWithHash(ctx, supplier, "", sdkIP, connID, ctlConnIdItem); err != nil {
						r.data.log.WithContext(ctx).Errorw("msg", "Failed to remove device connection by ctlConnId", "device_key", deviceKey, "field", field, "error", err)
					}
				}

				if ctlCursor == 0 {
					break
				}
			}
		}

		if cursor == 0 {
			break
		}
	}
	r.RemoveCtlUnavailable(ctx, originCtlConnId)
	return nil
}

// 从 deviceKey 反解析供应商和ip
func (r *deviceRepo) getSupplierIpFromDeviceKey(deviceKey string) (supplier, sdkIP string) {
	parts := strings.Split(deviceKey, ":")
	if len(parts) >= 3 {
		supplier = strings.ReplaceAll(parts[1], "{", "")
		supplier = strings.ReplaceAll(supplier, "}", "")
		sdkIP = parts[2]
	}
	return
}

// 从 fieldKey 反解析 ctlConnId 和 connId
func (r *deviceRepo) getCtlAndConnFromField(fieldKey string) (ctlConnId string, connId string) {
	parts := strings.Split(fieldKey, ":")
	if len(parts) >= 1 {
		ctlConnId = parts[0]
	}
	if len(parts) >= 2 {
		connId = parts[1]
	}
	return
}

// IsCtlUnavailable checks if a ctlConnId is marked as unavailable
func (r *deviceRepo) IsCtlUnavailable(ctx context.Context, ctlConnId string) (bool, error) {
	unavailableKey := "ctlunavailable"
	exists, err := r.data.rdb.SIsMember(ctx, unavailableKey, ctlConnId).Result()
	if err != nil {
		return false, errors.Wrap(err, "failed to check ctlunavailable status")
	}
	return exists, nil
}

// removeDeviceConnByCtlConnIdWithLua removes connections for a given ctlConnId from a device using a Lua script.
func (r *deviceRepo) removeDeviceConnByCtlConnIdWithLua(ctx context.Context, deviceKey, ctlConnId string) error {
	const script = `
local device_key = KEYS[1]
local ctl_conn_id = ARGV[1]

local device_data = redis.call('GET', device_key)
if not device_data then
    return 0
end

local device = cjson.decode(device_data)
local sdk_conn = device.sdkConn
if not sdk_conn then
    return 0
end

local changed = false
local new_sdk_conn = {}
local total_conns = 0

for sdk_id, conn_infos in pairs(sdk_conn) do
    local new_conn_infos = {}
    for _, conn in ipairs(conn_infos) do
        if conn.ctlConnId ~= ctl_conn_id then
            table.insert(new_conn_infos, conn)
        else
            changed = true
        end
    end
    if #new_conn_infos > 0 then
        new_sdk_conn[sdk_id] = new_conn_infos
		total_conns = total_conns + #new_conn_infos
    else
		changed = true
	end
end

if changed then
    device.sdkConn = new_sdk_conn
    device.lastSeen = ARGV[2] -- Current timestamp

    if total_conns > 0 then
        local updated_device_data = cjson.encode(device)
        redis.call('SET', device_key, updated_device_data)
    else
        redis.call('DEL', device_key)
        -- Also remove from location set and update weights
        local supplier = device.supplier
        local location = device.location
        local sdk_ip = device.sdkIp
        if supplier and location and sdk_ip then
            local location_key = string.format("location:%s:%s", supplier, location)
            redis.call('SREM', location_key, sdk_ip)
            local weight_key = string.format("mixed_weight:%s", supplier)
            redis.call('ZINCRBY', weight_key, -1, location)
            redis.call('ZREMRANGEBYSCORE', weight_key, '-inf', '0')
        end
    end
    return 1
end
return 0
`
	_, err := r.data.rdb.Eval(ctx, script, []string{deviceKey}, ctlConnId, time.Now().Format(time.RFC3339Nano)).Result()
	if err != nil && err != redis.Nil {
		return errors.Wrapf(err, "failed to execute lua script for device key %s", deviceKey)
	}
	return nil
}

// RemoveConnectionsByCtlConnIdV2 iterates through devices and uses a Lua script to remove connections.
func (r *deviceRepo) RemoveConnectionsByCtlConnIdV2(ctx context.Context, ctlConnId string) error {
	var cursor uint64
	var err error

	for {
		var keys []string
		keys, cursor, err = r.data.rdb.Scan(ctx, cursor, "device:*", 100).Result()
		if err != nil {
			return errors.Wrap(err, "failed to scan device keys")
		}

		for _, deviceKey := range keys {
			err := r.removeDeviceConnByCtlConnIdWithLua(ctx, deviceKey, ctlConnId)
			if err != nil {
				// Log error but continue processing other keys
				r.data.log.WithContext(ctx).Errorf("Failed to process device key %s with Lua script: %v", deviceKey, err)
			}
		}

		if cursor == 0 {
			break
		}
	}

	// Clean up the ctlConnId from the unavailable set after processing
	unavailableKey := "ctlunavailable"
	if err := r.data.rdb.SRem(ctx, unavailableKey, ctlConnId).Err(); err != nil {
		r.data.log.WithContext(ctx).Errorf("Failed to remove ctlConnId %s from unavailable set: %v", ctlConnId, err)
	}

	return nil
}

func (r *deviceRepo) RemoveCtlUnavailable(ctx context.Context, ctlConnId string) error {
	unavailableKey := "ctlunavailable"
	if err := r.data.rdb.SRem(ctx, unavailableKey, ctlConnId).Err(); err != nil {
		r.data.log.WithContext(ctx).Errorf("Failed to remove ctlConnId %s from unavailable set: %v", ctlConnId, err)
	}

	return nil
}

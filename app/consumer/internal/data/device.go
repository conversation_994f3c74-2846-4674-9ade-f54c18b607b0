package data

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"

	"sh_proxy/pkg/types"
)

type ConnInfo = types.ConnInfo

// HashConnInfo Hash存储的连接信息
type HashConnInfo struct {
	SdkId     string `json:"sdkId"`
	Host      string `json:"host"`
	ConnId    string `json:"connId"`
	CtlConnId string `json:"ctlConnId"`
	Timestamp int64  `json:"timestamp"`
}

// ToJSON 转换为JSON字符串
func (h *HashConnInfo) ToJSON() string {
	data, _ := json.Marshal(h)
	return string(data)
}

// FromJSON 从JSON字符串解析
func (h *HashConnInfo) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), h)
}

// ToConnInfo 转换为ConnInfo
func (h *HashConnInfo) ToConnInfo() *ConnInfo {
	return &ConnInfo{
		Host:      h.Host,
		ConnId:    h.ConnId,
		Timestamp: h.Timestamp,
		CtlConnId: h.CtlConnId,
	}
}

// NewHashConnInfo 从ConnInfo创建HashConnInfo
func NewHashConnInfo(sdkId string, connInfo *ConnInfo) *HashConnInfo {
	return &HashConnInfo{
		SdkId:     sdkId,
		Host:      connInfo.Host,
		ConnId:    connInfo.ConnId,
		CtlConnId: connInfo.CtlConnId,
		Timestamp: time.Now().Unix(),
	}
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	SdkIP     string                       `json:"sdkIp"`
	Supplier  string                       `json:"supplier"`
	Location  string                       `json:"location"`
	SdkConn   map[string][]*types.ConnInfo `json:"sdkConn"` // key 为 sdkId, value 为连接信息
	LastSeen  time.Time                    `json:"lastSeen"`
	CreatedAt time.Time                    `json:"createdAt"`
}

// DeviceMeta 设备元信息
type DeviceMeta struct {
	SdkIP     string `json:"sdkIp"`
	Supplier  string `json:"supplier"`
	Location  string `json:"location"`
	LastSeen  int64  `json:"lastSeen"`
	CreatedAt int64  `json:"createdAt"`
}

// ToJSON 转换为JSON字符串
func (d *DeviceMeta) ToJSON() string {
	data, _ := json.Marshal(d)
	return string(data)
}

// FromJSON 从JSON字符串解析
func (d *DeviceMeta) FromJSON(jsonStr string) error {
	return json.Unmarshal([]byte(jsonStr), d)
}

// GetDeviceKey 获取设备的Redis key
func (d *DeviceInfo) GetDeviceKey() string {
	return fmt.Sprintf("device:%s:%s", d.Supplier, d.SdkIP)
}

// GetDeviceMetaKey 获取设备元信息的Redis key
func GetDeviceMetaKey(supplier, sdkIP string) string {
	return fmt.Sprintf("device:%s:%s:meta", supplier, sdkIP)
}

// GetHashFieldKey 获取Hash字段key
func GetHashFieldKey(ctlConnId, sdkId, connId string) string {
	return fmt.Sprintf("%s:%s:%s", ctlConnId, sdkId, connId)
}

// GetLocationKey 获取位置的Redis key
func GetLocationKey(supplier, location string) string {
	return fmt.Sprintf("location:%s:%s", supplier, location)
}

// GetSessionKey 获取会话的Redis key
func GetSessionKey(authUser, session string) string {
	return fmt.Sprintf("session:%s:%s", authUser, session)
}

// LocationStats 位置统计信息
// type LocationStats struct {
// 	Supplier    string `json:"supplier"`
// 	Location    string `json:"location"`
// 	DeviceCount int64  `json:"deviceCount"`
// 	Weight      int64  `json:"weight"`
// }

// WeightedLocation 加权位置信息
type WeightedLocation struct {
	Supplier string `json:"supplier"`
	Location string `json:"location"`
	Weight   int64  `json:"weight"`
}

// DeviceRepo 设备仓库接口
type DeviceRepo interface {
	// 连接管理
	AddDevice(ctx context.Context, device *DeviceInfo, sdkID string, connInfo *ConnInfo) error
	RemoveDevice(ctx context.Context, supplier, sdkID, sdkIP, connID string) error
	GetDevice(ctx context.Context, supplier, sdkIP string) (*DeviceInfo, error)
	SetCtlUnavailable(ctx context.Context, ctlConnId string) error
	RemoveConnectionsByCtlConnId(ctx context.Context, ctlConnId string) error
	RemoveConnectionsByCtlConnIdV2(ctx context.Context, ctlConnId string) error
	IsCtlUnavailable(ctx context.Context, ctlConnId string) (bool, error)

	// 位置查询
	GetDevicesByLocation(ctx context.Context, supplier, location string) ([]*DeviceInfo, error)
	GetRandomDeviceForMixedMode(ctx context.Context, supplier string) (*DeviceInfo, error)

	// 位置统计和管理
	GetLocationDeviceCount(ctx context.Context, supplier, location string) (int64, error)
	UpdateLocationStats(ctx context.Context, supplier, location string, deviceCount int64) error

	// 混播模式权重管理
	UpdateLocationWeight(ctx context.Context, supplier, location string, weight int64) error
	GetRandomLocationByWeight(ctx context.Context, supplier string) (string, error)

	// 会话管理
	SetSessionDevice(ctx context.Context, authUser, session, sdkID, sdkIP string, ttl time.Duration) error
	GetSessionDevice(ctx context.Context, authUser, session string) (string, string, error)
}

type deviceRepo struct {
	data        *Data
	lockManager *LockManager
	lms         *LocationMappingService
}

// NewDeviceRepo 创建设备仓库
func NewDeviceRepo(data *Data, lms *LocationMappingService) DeviceRepo {
	return &deviceRepo{
		data:        data,
		lockManager: data.lockManager,
		lms:         lms,
	}
}

// AddDevice 添加设备连接（使用Hash存储，无需锁）
func (r *deviceRepo) AddDevice(ctx context.Context, device *DeviceInfo, sdkID string, connInfo *ConnInfo) error {
	return r.addDeviceWithHash(ctx, device, sdkID, connInfo)
}

// addDeviceWithHash 使用Hash存储添加设备连接
func (r *deviceRepo) addDeviceWithHash(ctx context.Context, device *DeviceInfo, sdkID string, connInfo *ConnInfo) error {
	deviceKey := device.GetDeviceKey()
	metaKey := GetDeviceMetaKey(device.Supplier, device.SdkIP)
	fieldKey := GetHashFieldKey(connInfo.CtlConnId, sdkID, connInfo.ConnId)

	// 创建Hash连接信息
	hashConn := NewHashConnInfo(sdkID, connInfo)

	// 使用事务确保原子性
	pipe := r.data.rdb.TxPipeline()

	// 1. 添加连接到Hash
	pipe.HSet(ctx, deviceKey, fieldKey, hashConn.ToJSON())

	// // 2. 更新设备元信息
	// deviceMeta := &DeviceMeta{
	// 	SdkIP:     device.SdkIP,
	// 	Supplier:  device.Supplier,
	// 	Location:  device.Location,
	// 	LastSeen:  time.Now().Unix(),
	// 	CreatedAt: time.Now().Unix(),
	// }

	// 检查是否是新设备
	// exists, err := r.data.rdb.Exists(ctx, metaKey).Result()
	// if err == nil && exists > 0 {
	// 	// 设备已存在，只更新LastSeen
	// 	pipe.HSet(ctx, metaKey, "lastSeen", deviceMeta.LastSeen)
	// } else {
	// 	// 新设备，设置完整元信息
	// 	pipe.HMSet(ctx, metaKey, map[string]interface{}{
	// 		"sdkIp":     deviceMeta.SdkIP,
	// 		"supplier":  deviceMeta.Supplier,
	// 		"location":  deviceMeta.Location,
	// 		"lastSeen":  deviceMeta.LastSeen,
	// 		"createdAt": deviceMeta.CreatedAt,
	// 	})
	// }

	// 3. 添加到位置集合
	locationKey := GetLocationKey(device.Supplier, device.Location)
	pipe.SAdd(ctx, locationKey, device.SdkIP)

	// 4. 更新混播权重索引（仅新设备）
	if exists == 0 {
		r.updateMixedModeWeight(ctx, pipe, device.Supplier, device.Location, 1)
		r.lms.AddDeviceLocationMapping(ctx, pipe, device.Supplier, device.Location)
	}

	// 执行事务
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute redis transaction: %w", err)
	}

	r.data.log.Infof("Device connection added using hash: %s:%s, fieldKey: %s, location: %s",
		device.SdkIP, sdkID, fieldKey, device.Location)

	return nil
}

// addDeviceWithoutLock 不使用锁的添加设备方法（内部使用）
func (r *deviceRepo) addDeviceWithoutLock(ctx context.Context, device *DeviceInfo, sdkID string, connInfo *ConnInfo) error {
	deviceKey := device.GetDeviceKey()
	locationKey := GetLocationKey(device.Supplier, device.Location)

	// 获取现有设备信息
	existingDevice, _ := r.GetDevice(ctx, device.Supplier, device.SdkIP)
	if existingDevice != nil {
		device.SdkConn = existingDevice.SdkConn
		device.CreatedAt = existingDevice.CreatedAt
	} else {
		device.CreatedAt = time.Now()
	}
	// 不存在连接信息，则初始化
	if device.SdkConn == nil {
		device.SdkConn = make(map[string][]*ConnInfo)
	}

	// 检查连接是否已存在
	if _, exists := device.SdkConn[sdkID]; exists {
		for _, conn := range device.SdkConn[sdkID] {
			if conn.ConnId == connInfo.ConnId {
				r.data.log.Warnf("Connection ID already exists: %s:%s, connID: %s",
					device.SdkIP, sdkID, connInfo.ConnId)
				return nil // 连接已存在，不需要重复添加
			}
		}
		device.SdkConn[sdkID] = append(device.SdkConn[sdkID], connInfo)
	} else {
		device.SdkConn[sdkID] = []*ConnInfo{connInfo}
	}

	device.LastSeen = time.Now()

	// 使用事务确保原子性
	pipe := r.data.rdb.TxPipeline()

	// 序列化设备信息
	deviceData, err := json.Marshal(device)
	if err != nil {
		return fmt.Errorf("failed to marshal device info: %w", err)
	}

	// 存储设备信息
	pipe.Set(ctx, deviceKey, deviceData, redis.KeepTTL)

	// 添加到位置集合
	pipe.SAdd(ctx, locationKey, device.SdkIP)

	// 更新位置统计信息
	// statsKey := fmt.Sprintf("location_stats:%s:%s", device.Supplier, device.Location)
	// pipe.Incr(ctx, statsKey)
	// pipe.Expire(ctx, statsKey, 24*time.Hour)

	// 新增 ip 时，增加权重
	if existingDevice == nil {
		// 更新混播权重索引
		r.updateMixedModeWeight(ctx, pipe, device.Supplier, device.Location, 1)
		// 更新对应 location 权重
		r.lms.AddDeviceLocationMapping(ctx, pipe, device.Supplier, device.Location)
	}

	// 执行事务
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute redis transaction: %w", err)
	}

	r.data.log.Infof("Device connection added: %s:%s, connID: %s, location: %s, total_connections: %d",
		device.SdkIP, sdkID, connInfo.ConnId, device.Location, len(device.SdkConn[sdkID]))

	return nil
}

// RemoveDevice 移除设备连接（使用Hash存储，无需锁）
func (r *deviceRepo) RemoveDevice(ctx context.Context, supplier, sdkID, sdkIP, connID string) error {
	return r.removeDeviceWithHash(ctx, supplier, sdkID, sdkIP, connID)
}

// removeDeviceWithHash 使用Hash存储移除设备连接
func (r *deviceRepo) removeDeviceWithHash(ctx context.Context, supplier, sdkID, sdkIP, connID string) error {
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, sdkIP)
	metaKey := GetDeviceMetaKey(supplier, sdkIP)

	// 需要先获取CtlConnId来构建完整的fieldKey
	// 由于我们不知道确切的CtlConnId，需要遍历所有字段找到匹配的连接
	allFields, err := r.data.rdb.HGetAll(ctx, deviceKey).Result()
	if err != nil {
		if err == redis.Nil {
			r.data.log.WithContext(ctx).Warnf("Device not found for removal: %s:%s", sdkID, sdkIP)
			return nil
		}
		return fmt.Errorf("failed to get device connections: %w", err)
	}

	var fieldToRemove string
	var ctlConnId string

	// 查找匹配的连接
	for fieldKey, connJSON := range allFields {
		var hashConn HashConnInfo
		if err := hashConn.FromJSON(connJSON); err != nil {
			continue
		}

		if hashConn.SdkId == sdkID && hashConn.ConnId == connID {
			fieldToRemove = fieldKey
			ctlConnId = hashConn.CtlConnId
			break
		}
	}

	if fieldToRemove == "" {
		r.data.log.WithContext(ctx).Warnf("Connection not found for removal: %s:%s:%s", supplier, sdkID, connID)
		return nil
	}

	// 使用事务移除连接
	pipe := r.data.rdb.TxPipeline()

	// 1. 从Hash中移除连接
	pipe.HDel(ctx, deviceKey, fieldToRemove)

	// 2. 更新设备元信息的LastSeen
	pipe.HSet(ctx, metaKey, "lastSeen", time.Now().Unix())

	// 3. 检查是否还有其他连接
	remainingCount := len(allFields) - 1
	if remainingCount == 0 {
		// 没有其他连接了，清理设备相关数据
		r.cleanupEmptyDevice(ctx, pipe, supplier, sdkIP)
	}

	// 执行事务
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute redis transaction: %w", err)
	}

	r.data.log.Infof("Device connection removed using hash: %s:%s, fieldKey: %s, remaining: %d",
		sdkIP, sdkID, fieldToRemove, remainingCount)

	return nil
}

// cleanupEmptyDevice 清理空设备
func (r *deviceRepo) cleanupEmptyDevice(ctx context.Context, pipe redis.Pipeliner, supplier, sdkIP string) {
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, sdkIP)
	metaKey := GetDeviceMetaKey(supplier, sdkIP)

	// 获取设备位置信息用于清理索引
	metaData, err := r.data.rdb.HGetAll(ctx, metaKey).Result()
	if err == nil && len(metaData) > 0 {
		location := metaData["location"]
		if location != "" {
			// 从位置集合中移除
			locationKey := GetLocationKey(supplier, location)
			pipe.SRem(ctx, locationKey, sdkIP)

			// 更新混播权重索引
			r.updateMixedModeWeight(ctx, pipe, supplier, location, -1)

			// 更新位置映射
			r.lms.RemoveDeviceLocationMapping(ctx, pipe, supplier, location)
		}
	}

	// 删除设备Hash和元信息
	pipe.Del(ctx, deviceKey)
	pipe.Del(ctx, metaKey)
}

// removeDeviceWithoutLock 不使用锁的移除设备方法（内部使用）
func (r *deviceRepo) removeDeviceWithoutLock(ctx context.Context, supplier, sdkID, sdkIP, connID string) error {
	// 获取现有设备信息
	device, err := r.GetDevice(ctx, supplier, sdkIP)
	if err != nil || device == nil {
		r.data.log.WithContext(ctx).Warnf("Device not found for removal: %s:%s", sdkID, sdkIP)
		return nil // 设备不存在，认为已经移除
	}

	if device.SdkConn == nil || len(device.SdkConn) == 0 {
		r.data.log.WithContext(ctx).Warnf("Device no sdk for removal: %s:%s", sdkID, sdkIP)
		return nil // 设备不存在，认为已经移除
	}
	if _, exists := device.SdkConn[sdkID]; !exists {
		r.data.log.WithContext(ctx).Warnf("Device sdk not found for removal: %s:%s", sdkID, sdkIP)
		return nil // 设备不存在，认为已经移除
	}

	var (
		existsConns = device.SdkConn[sdkID]
		newConns    []*ConnInfo
	)
	for i, conn := range existsConns {
		if conn.ConnId != connID {
			newConns = append(newConns, existsConns[i])
		}
	}
	if len(existsConns) == len(newConns) {
		r.data.log.WithContext(ctx).Warnf("Device sdk conn not found for removal: %s:%s, connID: %s", sdkID, sdkIP, connID)
		return nil // 设备不存在，认为已经移除
	}

	device.SdkConn[sdkID] = newConns
	device.LastSeen = time.Now()
	if len(device.SdkConn[sdkID]) == 0 {
		delete(device.SdkConn, sdkID)
	}

	if err := r.removeDeviceFromRedis(ctx, supplier, sdkIP, device); err != nil {
		return err
	}
	r.data.log.WithContext(ctx).Infof("Device connection removed: %s:%s, connID: %s, remaining connections: %d",
		sdkID, sdkIP, connID, len(device.SdkConn[sdkID]))

	return nil
}

func (r *deviceRepo) removeDeviceFromRedis(ctx context.Context, supplier, sdkIP string, device *DeviceInfo) error {
	if supplier == "" {
		supplier = device.Supplier
	}
	if sdkIP == "" {
		sdkIP = device.SdkIP
	}
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, sdkIP)

	pipe := r.data.rdb.TxPipeline()

	// 如果还有其他连接，更新设备信息
	if len(device.SdkConn) > 0 {
		deviceData, err := json.Marshal(device)
		if err != nil {
			return fmt.Errorf("failed to marshal device info: %w", err)
		}
		pipe.Set(ctx, deviceKey, deviceData, redis.KeepTTL)
	} else {
		// 没有连接了，完全移除设备
		pipe.Del(ctx, deviceKey)

		// 从位置集合中移除
		locationKey := GetLocationKey(supplier, device.Location)
		pipe.SRem(ctx, locationKey, sdkIP)

		// 更新位置统计信息
		// statsKey := fmt.Sprintf("location_stats:%s:%s", device.Supplier, device.Location)
		// pipe.Decr(ctx, statsKey)

		// 更新混播权重索引
		r.updateMixedModeWeight(ctx, pipe, device.Supplier, device.Location, -1)
		// 更新对应 location 权重
		r.lms.RemoveDeviceLocationMapping(ctx, pipe, device.Supplier, device.Location)
	}

	// 执行事务
	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to execute redis transaction: %w", err)
	}

	return nil
}

// GetDevice 获取设备信息（支持Hash和JSON格式）
func (r *deviceRepo) GetDevice(ctx context.Context, supplier, sdkIP string) (*DeviceInfo, error) {
	// 先尝试从Hash格式读取
	if device, err := r.getDeviceFromHash(ctx, supplier, sdkIP); err == nil && device != nil {
		return device, nil
	}

	// 降级到旧的JSON格式
	return r.getDeviceFromJSON(ctx, supplier, sdkIP)
}

// getDeviceFromHash 从Hash格式获取设备信息
func (r *deviceRepo) getDeviceFromHash(ctx context.Context, supplier, sdkIP string) (*DeviceInfo, error) {
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, sdkIP)
	metaKey := GetDeviceMetaKey(supplier, sdkIP)

	// 1. 获取所有连接信息
	connMap, err := r.data.rdb.HGetAll(ctx, deviceKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get device connections: %w", err)
	}

	if len(connMap) == 0 {
		return nil, nil
	}

	// 2. 获取设备元信息
	metaMap, err := r.data.rdb.HGetAll(ctx, metaKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get device meta: %w", err)
	}

	// 3. 构建DeviceInfo
	device := &DeviceInfo{
		SdkIP:    sdkIP,
		Supplier: supplier,
		SdkConn:  make(map[string][]*types.ConnInfo),
	}

	// 解析元信息
	if location, ok := metaMap["location"]; ok {
		device.Location = location
	}
	if lastSeenStr, ok := metaMap["lastSeen"]; ok {
		if lastSeen, err := strconv.ParseInt(lastSeenStr, 10, 64); err == nil {
			device.LastSeen = time.Unix(lastSeen, 0)
		}
	}
	if createdAtStr, ok := metaMap["createdAt"]; ok {
		if createdAt, err := strconv.ParseInt(createdAtStr, 10, 64); err == nil {
			device.CreatedAt = time.Unix(createdAt, 0)
		}
	}

	// 4. 解析连接信息
	for fieldKey, connJSON := range connMap {
		var hashConn HashConnInfo
		if err := hashConn.FromJSON(connJSON); err != nil {
			r.data.log.WithContext(ctx).Warnf("Failed to parse connection info: %s, error: %v", fieldKey, err)
			continue
		}

		connInfo := hashConn.ToConnInfo()
		sdkId := hashConn.SdkId

		if device.SdkConn[sdkId] == nil {
			device.SdkConn[sdkId] = make([]*types.ConnInfo, 0)
		}
		device.SdkConn[sdkId] = append(device.SdkConn[sdkId], connInfo)
	}

	return device, nil
}

// getDeviceFromJSON 从JSON格式获取设备信息（兼容旧格式）
func (r *deviceRepo) getDeviceFromJSON(ctx context.Context, supplier, sdkIP string) (*DeviceInfo, error) {
	deviceKey := fmt.Sprintf("device:%s:%s", supplier, sdkIP)

	data, err := r.data.rdb.Get(ctx, deviceKey).Result()
	if err != nil {
		if err == redis.Nil {
			return nil, nil
		}
		return nil, err
	}

	var device DeviceInfo
	if err := json.Unmarshal([]byte(data), &device); err != nil {
		return nil, fmt.Errorf("failed to unmarshal device info: %w", err)
	}

	return &device, nil
}

// GetDevicesByLocation 根据位置获取设备列表
func (r *deviceRepo) GetDevicesByLocation(ctx context.Context, supplier, location string) ([]*DeviceInfo, error) {
	locationKey := GetLocationKey(supplier, location)

	sdkIPs, err := r.data.rdb.SMembers(ctx, locationKey).Result()
	if err != nil {
		return nil, err
	}

	var devices []*DeviceInfo
	for _, sdkIP := range sdkIPs {
		device, err := r.GetDevice(ctx, supplier, sdkIP)
		if err != nil || device == nil {
			// 设备不存在，从位置集合中清理
			r.data.rdb.SRem(ctx, locationKey, sdkIP)
			continue
		}

		// 检查设备是否有活跃连接
		if len(device.SdkConn) == 0 {
			// 没有活跃连接，从位置集合中清理
			r.data.rdb.SRem(ctx, locationKey, sdkIP)
			continue
		}

		devices = append(devices, device)
	}

	return devices, nil
}

func (r *deviceRepo) GetDeviceByLocation(ctx context.Context, supplier, location string) (*DeviceInfo, error) {
	locationKey := GetLocationKey(supplier, location)

	sdkIP, err := r.data.rdb.SRandMember(ctx, locationKey).Result()
	if err != nil {
		return nil, err
	}

	device, err := r.GetDevice(ctx, supplier, sdkIP)
	if err != nil || device == nil {
		// 设备不存在，从位置集合中清理
		r.data.rdb.SRem(ctx, locationKey, sdkIP)
		return nil, nil
	}

	// 检查设备是否有活跃连接
	if len(device.SdkConn) == 0 {
		// 没有活跃连接，从位置集合中清理
		r.data.rdb.SRem(ctx, locationKey, sdkIP)
		return nil, nil
	}

	return device, nil
}

// GetRandomDevicesFromLocations 从所有位置中随机获取设备（混播模式）
// func (r *deviceRepo) GetRandomDevicesFromLocations(ctx context.Context, supplier string, maxDevices int) ([]*DeviceInfo, error) {
// 	// 获取所有位置的统计信息
// 	locationStats, err := r.GetAllLocationStats(ctx)
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to get location stats: %w", err)
// 	}
//
// 	if len(locationStats) == 0 {
// 		return []*DeviceInfo{}, nil
// 	}
//
// 	// 计算总权重
// 	var totalWeight int64
// 	for _, stat := range locationStats {
// 		totalWeight += stat.Weight
// 	}
//
// 	if totalWeight == 0 {
// 		return []*DeviceInfo{}, nil
// 	}
//
// 	var selectedDevices []*DeviceInfo
// 	selectedCount := 0
//
// 	// 基于权重随机选择位置，然后从该位置随机选择设备
// 	for selectedCount < maxDevices && len(locationStats) > 0 {
// 		// 加权随机选择位置
// 		selectedLocation := r.weightedRandomSelect(locationStats, totalWeight)
// 		if selectedLocation == nil {
// 			break
// 		}
//
// 		// 从选中的位置获取设备
// 		devices, err := r.GetDevicesByLocation(ctx, supplier, selectedLocation.Location)
// 		if err != nil || len(devices) == 0 {
// 			// 移除无效的位置统计
// 			r.removeLocationFromStats(locationStats, selectedLocation.Location)
// 			totalWeight -= selectedLocation.Weight
// 			continue
// 		}
//
// 		// 随机选择该位置的一个设备
// 		if len(devices) > 0 {
// 			randomIndex := rand.Intn(len(devices))
// 			selectedDevices = append(selectedDevices, devices[randomIndex])
// 			selectedCount++
// 		}
//
// 		// 减少该位置的权重，避免重复选择
// 		selectedLocation.Weight = selectedLocation.Weight / 2
// 		if selectedLocation.Weight <= 0 {
// 			r.removeLocationFromStats(locationStats, selectedLocation.Location)
// 		}
// 		totalWeight = r.recalculateTotalWeight(locationStats)
// 	}
//
// 	return selectedDevices, nil
// }

// GetAllLocationStats 获取所有位置的统计信息
// func (r *deviceRepo) GetAllLocationStats(ctx context.Context) ([]*LocationStats, error) {
// 	// 使用 SCAN 命令遍历所有位置统计键
// 	pattern := "location_stats:*"
// 	var cursor uint64
// 	var stats []*LocationStats
//
// 	for {
// 		keys, nextCursor, err := r.data.rdb.Scan(ctx, cursor, pattern, 100).Result()
// 		if err != nil {
// 			return nil, err
// 		}
//
// 		// 批量获取统计数据
// 		if len(keys) > 0 {
// 			pipe := r.data.rdb.Pipeline()
// 			cmds := make([]*redis.StringCmd, len(keys))
//
// 			for i, key := range keys {
// 				cmds[i] = pipe.Get(ctx, key)
// 			}
//
// 			_, err := pipe.Exec(ctx)
// 			if err != nil && err != redis.Nil {
// 				return nil, err
// 			}
//
// 			for i, cmd := range cmds {
// 				if cmd.Err() == nil {
// 					location := strings.TrimPrefix(keys[i], "location_stats:")
// 					if count, err := cmd.Int64(); err == nil && count > 0 {
// 						stats = append(stats, &LocationStats{
// 							Location:    location,
// 							DeviceCount: count,
// 							Weight:      count, // 权重等于设备数量
// 						})
// 					}
// 				}
// 			}
// 		}
//
// 		cursor = nextCursor
// 		if cursor == 0 {
// 			break
// 		}
// 	}
//
// 	return stats, nil
// }

// UpdateLocationStats 更新位置统计信息
func (r *deviceRepo) UpdateLocationStats(ctx context.Context, supplier, location string, deviceCount int64) error {
	statsKey := fmt.Sprintf("location_stats:%s:%s", supplier, location)
	return r.data.rdb.Set(ctx, statsKey, deviceCount, 24*time.Hour).Err()
}

// weightedRandomSelect 加权随机选择位置
// func (r *deviceRepo) weightedRandomSelect(stats []*LocationStats, totalWeight int64) *LocationStats {
// 	if totalWeight <= 0 {
// 		return nil
// 	}
//
// 	randomWeight := rand.Int63n(totalWeight)
// 	var currentWeight int64
//
// 	for _, stat := range stats {
// 		currentWeight += stat.Weight
// 		if randomWeight < currentWeight {
// 			return stat
// 		}
// 	}
//
// 	return nil
// }

// removeLocationFromStats 从统计列表中移除位置
// func (r *deviceRepo) removeLocationFromStats(stats []*LocationStats, location string) {
// 	for i, stat := range stats {
// 		if stat.Location == location {
// 			// 移除元素
// 			copy(stats[i:], stats[i+1:])
// 			stats = stats[:len(stats)-1]
// 			break
// 		}
// 	}
// }

// recalculateTotalWeight 重新计算总权重
// func (r *deviceRepo) recalculateTotalWeight(stats []*LocationStats) int64 {
// 	var total int64
// 	for _, stat := range stats {
// 		total += stat.Weight
// 	}
// 	return total
// }

// SetSessionDevice 设置会话绑定的设备
func (r *deviceRepo) SetSessionDevice(ctx context.Context, authUser, session, sdkID, sdkIP string, ttl time.Duration) error {
	sessionKey := GetSessionKey(authUser, session)
	deviceIdentifier := fmt.Sprintf("%s:%s", sdkID, sdkIP)

	return r.data.rdb.Set(ctx, sessionKey, deviceIdentifier, ttl).Err()
}

// GetSessionDevice 获取会话绑定的设备
func (r *deviceRepo) GetSessionDevice(ctx context.Context, authUser, session string) (string, string, error) {
	sessionKey := GetSessionKey(authUser, session)

	deviceIdentifier, err := r.data.rdb.Get(ctx, sessionKey).Result()
	if err != nil {
		if err == redis.Nil {
			return "", "", nil
		}
		return "", "", err
	}

	parts := strings.Split(deviceIdentifier, ":")
	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid device identifier: %s", deviceIdentifier)
	}

	return parts[0], parts[1], nil
}

// GetLocationDeviceCount 获取位置的设备数量
func (r *deviceRepo) GetLocationDeviceCount(ctx context.Context, supplier, location string) (int64, error) {
	locationKey := GetLocationKey(supplier, location)
	return r.data.rdb.SCard(ctx, locationKey).Result()
}

// GetRandomDeviceForMixedMode 混播模式：基于权重随机选择一个设备（高效版本）
func (r *deviceRepo) GetRandomDeviceForMixedMode(ctx context.Context, supplier string) (*DeviceInfo, error) {
	// 1. 基于权重随机选择一个位置
	selectedLocation, err := r.GetRandomLocationByWeight(ctx, supplier)
	if err != nil {
		return nil, fmt.Errorf("failed to select random location: %w", err)
	}

	if selectedLocation == "" {
		return nil, fmt.Errorf("no available locations for supplier: %s", supplier)
	}

	// 2. 从选中的位置随机选择一个设备
	selectedDevice, err := r.GetDeviceByLocation(ctx, supplier, selectedLocation)
	if err != nil {
		return nil, fmt.Errorf("failed to get device from location %s: %w", selectedLocation, err)
	}
	if selectedDevice == nil {
		return nil, fmt.Errorf("no devices available in location: %s", selectedLocation)
	}

	// 2. 从选中的位置随机选择一个设备
	// devices, err := r.GetDevicesByLocation(ctx, supplier, selectedLocation)
	// if err != nil {
	// 	return nil, fmt.Errorf("failed to get devices from location %s: %w", selectedLocation, err)
	// }
	//
	// if len(devices) == 0 {
	// 	// 位置没有设备，清理权重索引
	// 	r.cleanupLocationWeight(ctx, supplier, selectedLocation)
	// 	return nil, fmt.Errorf("no devices available in location: %s", selectedLocation)
	// }
	//
	// // 3. 随机选择一个设备
	// rand.Seed(time.Now().UnixNano())
	// selectedDevice := devices[rand.Intn(len(devices))]

	return selectedDevice, nil
}

// GetRandomLocationByWeight 基于权重随机选择位置
func (r *deviceRepo) GetRandomLocationByWeight(ctx context.Context, supplier string) (string, error) {
	// 使用 Redis 的 ZRANDMEMBER 命令基于权重随机选择
	// 权重索引键：mixed_weight:{supplier}
	weightKey := fmt.Sprintf("mixed_weight:%s", supplier)

	// 使用 ZRANDMEMBER 命令随机选择一个位置（基于分数权重）
	result, err := r.data.rdb.ZRandMemberWithScores(ctx, weightKey, 1).Result()
	if err != nil {
		if err == redis.Nil {
			return "", nil // 没有可用位置
		}
		return "", fmt.Errorf("failed to get random weighted location: %w", err)
	}

	if len(result) == 0 {
		return "", nil
	}

	return result[0].Member.(string), nil
}

// UpdateLocationWeight 更新位置权重
func (r *deviceRepo) UpdateLocationWeight(ctx context.Context, supplier, location string, weight int64) error {
	weightKey := fmt.Sprintf("mixed_weight:%s", supplier)

	if weight <= 0 {
		// 权重为0或负数，从权重索引中移除
		return r.data.rdb.ZRem(ctx, weightKey, location).Err()
	}

	// 更新权重
	return r.data.rdb.ZAdd(ctx, weightKey, redis.Z{
		Score:  float64(weight),
		Member: location,
	}).Err()
}

// updateMixedModeWeight 更新混播模式权重（内部方法）
func (r *deviceRepo) updateMixedModeWeight(ctx context.Context, pipe redis.Pipeliner, supplier, location string, delta int64) {
	weightKey := fmt.Sprintf("mixed_weight:%s", supplier)

	if delta > 0 {
		// 增加权重
		pipe.ZIncrBy(ctx, weightKey, float64(delta), location)
		// pipe.Expire(ctx, weightKey, redis.KeepTTL)
	} else {
		// 减少权重
		pipe.ZIncrBy(ctx, weightKey, float64(delta), location)
		// 如果权重降到0或以下，移除该位置
		pipe.ZRemRangeByScore(ctx, weightKey, "-inf", "0")
	}
}

// cleanupLocationWeight 清理无效的位置权重
func (r *deviceRepo) cleanupLocationWeight(ctx context.Context, supplier, location string) {
	weightKey := fmt.Sprintf("mixed_weight:%s", supplier)
	r.data.rdb.ZRem(ctx, weightKey, location)
}

// SetCtlUnavailable sets a ctlConnId as unavailable
func (r *deviceRepo) SetCtlUnavailable(ctx context.Context, ctlConnId string) error {
	unavailableKey := "ctlunavailable"

	if err := r.data.rdb.SAdd(ctx, unavailableKey, ctlConnId).Err(); err != nil {
		return errors.Wrap(err, "failed to add ctlunavailable")
	}
	return nil
}

// RemoveConnectionsByCtlConnId removes all connections associated with the given ctlConnId
func (r *deviceRepo) RemoveConnectionsByCtlConnId(ctx context.Context, ctlConnId string) error {
	var cursor uint64
	var keys []string
	var err error

	for {
		keys, cursor, err = r.data.rdb.Scan(ctx, cursor, "device:*", 100).Result()
		if err != nil {
			return errors.Wrap(err, "failed to scan device keys")
		}

		for _, deviceKey := range keys {
			lockKey := "lock:" + deviceKey
			lockTTL := 30 * time.Second

			// Use distributed lock for each device
			err = r.lockManager.WithLockRetry(ctx, lockKey, lockTTL, 3, 100*time.Millisecond, func() error {
				deviceData, err := r.data.rdb.Get(ctx, deviceKey).Result()
				if err != nil {
					if err == redis.Nil {
						return nil // Device already gone, skip
					}
					return err
				}

				var device DeviceInfo
				if err := json.Unmarshal([]byte(deviceData), &device); err != nil {
					r.data.log.WithContext(ctx).Errorf("Failed to unmarshal device info for key %s: %v", deviceKey, err)
					return nil // Skip this malformed device
				}

				changed := false
				newSdkConn := make(map[string][]*types.ConnInfo)
				for sdkID, connInfos := range device.SdkConn {
					var newConnInfos []*types.ConnInfo
					for _, conn := range connInfos {
						if conn.CtlConnId != ctlConnId { // Assuming ctlConnId directly maps to ConnInfo.CtlConnId
							newConnInfos = append(newConnInfos, conn)
						} else {
							changed = true
						}
					}
					if len(newConnInfos) > 0 {
						newSdkConn[sdkID] = newConnInfos
					} else {
						changed = true // All connections for this SDK removed
					}
				}

				if changed {
					device.SdkConn = newSdkConn
					device.LastSeen = time.Now()
					if err := r.removeDeviceFromRedis(ctx, device.Supplier, device.SdkIP, &device); err != nil {
						return err
					}
					r.data.log.WithContext(ctx).Infow("msg", "Removed connections for ctlConnId", "ctl_conn_id", ctlConnId, "device_key", deviceKey)
				}
				return nil
			})
			if err != nil {
				r.data.log.WithContext(ctx).Errorw("msg", "Failed to process device key for ctlConnId removal", "device_key", deviceKey, "error", err)
				// Continue to next device, but log the error
			}
		}

		if cursor == 0 {
			break
		}
	}
	r.RemoveCtlUnavailable(ctx, ctlConnId)
	return nil
}

// IsCtlUnavailable checks if a ctlConnId is marked as unavailable
func (r *deviceRepo) IsCtlUnavailable(ctx context.Context, ctlConnId string) (bool, error) {
	unavailableKey := "ctlunavailable"
	exists, err := r.data.rdb.SIsMember(ctx, unavailableKey, ctlConnId).Result()
	if err != nil {
		return false, errors.Wrap(err, "failed to check ctlunavailable status")
	}
	return exists, nil
}

// removeDeviceConnByCtlConnIdWithLua removes connections for a given ctlConnId from a device using a Lua script.
func (r *deviceRepo) removeDeviceConnByCtlConnIdWithLua(ctx context.Context, deviceKey, ctlConnId string) error {
	const script = `
local device_key = KEYS[1]
local ctl_conn_id = ARGV[1]

local device_data = redis.call('GET', device_key)
if not device_data then
    return 0
end

local device = cjson.decode(device_data)
local sdk_conn = device.sdkConn
if not sdk_conn then
    return 0
end

local changed = false
local new_sdk_conn = {}
local total_conns = 0

for sdk_id, conn_infos in pairs(sdk_conn) do
    local new_conn_infos = {}
    for _, conn in ipairs(conn_infos) do
        if conn.ctlConnId ~= ctl_conn_id then
            table.insert(new_conn_infos, conn)
        else
            changed = true
        end
    end
    if #new_conn_infos > 0 then
        new_sdk_conn[sdk_id] = new_conn_infos
		total_conns = total_conns + #new_conn_infos
    else
		changed = true
	end
end

if changed then
    device.sdkConn = new_sdk_conn
    device.lastSeen = ARGV[2] -- Current timestamp

    if total_conns > 0 then
        local updated_device_data = cjson.encode(device)
        redis.call('SET', device_key, updated_device_data)
    else
        redis.call('DEL', device_key)
        -- Also remove from location set and update weights
        local supplier = device.supplier
        local location = device.location
        local sdk_ip = device.sdkIp
        if supplier and location and sdk_ip then
            local location_key = string.format("location:%s:%s", supplier, location)
            redis.call('SREM', location_key, sdk_ip)
            local weight_key = string.format("mixed_weight:%s", supplier)
            redis.call('ZINCRBY', weight_key, -1, location)
            redis.call('ZREMRANGEBYSCORE', weight_key, '-inf', '0')
        end
    end
    return 1
end
return 0
`
	_, err := r.data.rdb.Eval(ctx, script, []string{deviceKey}, ctlConnId, time.Now().Format(time.RFC3339Nano)).Result()
	if err != nil && err != redis.Nil {
		return errors.Wrapf(err, "failed to execute lua script for device key %s", deviceKey)
	}
	return nil
}

// RemoveConnectionsByCtlConnIdV2 iterates through devices and uses a Lua script to remove connections.
func (r *deviceRepo) RemoveConnectionsByCtlConnIdV2(ctx context.Context, ctlConnId string) error {
	var cursor uint64
	var err error

	for {
		var keys []string
		keys, cursor, err = r.data.rdb.Scan(ctx, cursor, "device:*", 100).Result()
		if err != nil {
			return errors.Wrap(err, "failed to scan device keys")
		}

		for _, deviceKey := range keys {
			err := r.removeDeviceConnByCtlConnIdWithLua(ctx, deviceKey, ctlConnId)
			if err != nil {
				// Log error but continue processing other keys
				r.data.log.WithContext(ctx).Errorf("Failed to process device key %s with Lua script: %v", deviceKey, err)
			}
		}

		if cursor == 0 {
			break
		}
	}

	// Clean up the ctlConnId from the unavailable set after processing
	unavailableKey := "ctlunavailable"
	if err := r.data.rdb.SRem(ctx, unavailableKey, ctlConnId).Err(); err != nil {
		r.data.log.WithContext(ctx).Errorf("Failed to remove ctlConnId %s from unavailable set: %v", ctlConnId, err)
	}

	return nil
}

func (r *deviceRepo) RemoveCtlUnavailable(ctx context.Context, ctlConnId string) error {
	unavailableKey := "ctlunavailable"
	if err := r.data.rdb.SRem(ctx, unavailableKey, ctlConnId).Err(); err != nil {
		r.data.log.WithContext(ctx).Errorf("Failed to remove ctlConnId %s from unavailable set: %v", ctlConnId, err)
	}

	return nil
}

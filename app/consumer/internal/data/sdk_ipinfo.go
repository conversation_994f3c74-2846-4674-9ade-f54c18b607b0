package data

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"go.mongodb.org/mongo-driver/mongo"
	"gorm.io/gorm"

	"sh_proxy/app/consumer/internal/biz"
	"sh_proxy/app/consumer/internal/conf"
	"sh_proxy/app/consumer/internal/data/models"

	"sh_proxy/app/consumer/internal/model"
)

type sdkIpinfoRepo struct {
	data *Data
	log  *log.Helper
	mdb  *mongo.Database
	ck   *gorm.DB
}

// NewSdkIpinfoRepo 创建 SdkIpinfoRepo
func NewSdkIpinfoRepo(data *Data, cfg *conf.Bootstrap, logger log.Logger) *sdkIpinfoRepo {
	return &sdkIpinfoRepo{
		data: data,
		log:  log.NewHelper(log.With(logger, "module", "data/sdk-ipinfo-repo")),
		mdb:  data.mdb.Database(cfg.Data.Mongodb.DbName),
		ck:   data.ck,
	}
}

// CreateSdkIpinfo 将 SdkIpinfo 保存到 MongoDB
func (r *sdkIpinfoRepo) CreateSdkIpinfo(ctx context.Context, info *biz.SdkIpinfo) error {
	modelInfo := &model.SdkIpinfo{
		IP:          info.IP,
		Country:     info.Country,
		CountryCode: info.CountryCode,
		Region:      info.Region,
		RegionName:  info.RegionName,
		City:        info.City,
		District:    info.District,
		Zip:         info.Zip,
		Lat:         info.Lat,
		Lon:         info.Lon,
		Timezone:    info.Timezone,
		Offset:      info.Offset,
		Currency:    info.Currency,
		Proxy:       info.Proxy,
		Hosting:     info.Hosting,
		Asname:      info.Asname,
		As:          info.As,
		ISP:         info.ISP,
		Mobile:      info.Mobile,
		CreatedAt:   info.CreatedAt,
	}

	// 1. 写入 MongoDB
	collection := r.mdb.Collection(modelInfo.TableName())
	_, err := collection.InsertOne(ctx, modelInfo)
	if err != nil {
		r.log.WithContext(ctx).Errorf("failed to insert SdkIpinfo to MongoDB: %v", err)
	}
	return err
}

// BatchCreateSdkIpInfoCK 批量将 SdkIpinfo 写入 ClickHouse
func (r *sdkIpinfoRepo) BatchCreateSdkIpInfoCK(ctx context.Context, infos []*models.SdkIpinfo) error {
	if len(infos) == 0 {
		return nil
	}
	return r.ck.WithContext(ctx).CreateInBatches(infos, 10000).Error
}

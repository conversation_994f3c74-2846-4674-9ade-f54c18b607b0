package data

import (
	"context"
	"os"

	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"gorm.io/driver/clickhouse"
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/driver/sqlserver"
	"gorm.io/gorm"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"

	v1 "sh_proxy/api/consumer/v1"
	"sh_proxy/app/consumer/internal/conf"
	contextlog "sh_proxy/pkg/log"
	"sh_proxy/pkg/utils"
)

// ProviderSet is data providers.
var ProviderSet = wire.NewSet(
	NewData,
	NewMongoClient,
	NewRabbitMQClient,
	NewConsumerHttpClient,
	NewGormClient,
	NewClickHouseBatchWriterV2,

	NewIPInfoRepo,
	NewDeviceRepo,
	NewEventLogRepo,

	NewLocationMappingService,
)

// Data .
type Data struct {
	log *contextlog.Helper

	rdb                *redis.Client
	mdb                *mongo.Client
	MQConn             *MQConn
	consumerHttpClient v1.ConsumerHTTPClient

	lockManager *LockManager
	crLock      *LockManager
	ck          *gorm.DB
	Batcher     *ClickHouseBatchWriterV2
}

// NewData .
func NewData(c *conf.Data, logger log.Logger, mqConn *MQConn, mdb *mongo.Client, ck *gorm.DB, chc v1.ConsumerHTTPClient, batcher *ClickHouseBatchWriterV2) (*Data, func(), error) {
	data := &Data{
		log:                contextlog.NewHelper(log.With(logger, "module", "data/consumer")),
		MQConn:             mqConn,
		mdb:                mdb,
		ck:                 ck,
		consumerHttpClient: chc,
		Batcher:            batcher,
	}

	redisClient, err := NewRedisClient(c.Redis, logger)
	if err != nil {
		return nil, nil, errors.Wrap(err, "new redis client")
	}
	data.rdb = redisClient

	data.lockManager = NewLockManager(redisClient)

	cleanup := func() {
		data.log.Info("closing the data resources")
		if data.MQConn != nil {
			data.MQConn.Close()
		}
		redisClient.Close()
		if mdb != nil {
			mdb.Disconnect(context.Background())
		}
	}
	return data, cleanup, nil
}

// NewRedisClient 创建Redis客户端
func NewRedisClient(cfg *conf.Data_Redis, logger log.Logger) (*redis.Client, error) {
	// l := log.NewHelper(log.With(logger, "module", "data/user/redis"))
	rdb := redis.NewClient(&redis.Options{
		Addr:         cfg.Addr,
		Username:     cfg.Username,
		Password:     cfg.Password,
		DB:           int(cfg.Db),
		DialTimeout:  cfg.DialTimeout.AsDuration(),
		WriteTimeout: cfg.WriteTimeout.AsDuration(),
		ReadTimeout:  cfg.ReadTimeout.AsDuration(),
	})
	if rdb == nil {
		return nil, errors.New("failed opening connection to redis")
	}
	// rdb.AddHook(redisotel.NewTracingHook())
	if _, err := rdb.Ping(context.Background()).Result(); err != nil {
		return nil, errors.Wrap(err, "failed to ping redis")
	}
	// l.Info("redis connected")

	return rdb, nil
}

func NewMongoClient(cfg *conf.Bootstrap, logger log.Logger) (*mongo.Client, error) {
	l := log.NewHelper(log.With(logger, "module", "data/user/mongo"))
	myRegion := os.Getenv("MY_REGION")
	if myRegion != "sg" && myRegion != "" {
		l.Infof("%s not in sg , skip mongodb", myRegion)
		return nil, nil
	}
	// 创建客户端
	ctx, cancel := context.WithTimeout(context.Background(), cfg.Data.Mongodb.Timeout.AsDuration())
	defer cancel()

	clientOptions := options.Client().ApplyURI(cfg.Data.Mongodb.Uri)
	client, err := mongo.Connect(ctx, clientOptions)
	if err != nil {
		l.Errorf("failed to connect to mongodb, %v", err)
		return nil, err
	}

	if utils.IsK8sEnv() {
		// 验证连接
		if err = client.Ping(ctx, nil); err != nil {
			l.Errorf("failed to ping mongodb, %v", err)
			return nil, err
		}
	}

	l.Info("mongodb connected")

	return client, nil
}

// NewGormClient 创建数据库客户端
func NewGormClient(cfg *conf.Bootstrap, logger log.Logger) *gorm.DB {
	l := log.NewHelper(log.With(logger, "module", "gorm/data/consumer-service"))

	var driver gorm.Dialector
	switch cfg.Data.Database.Driver {
	default:
		fallthrough
	case "mysql":
		driver = mysql.Open(cfg.Data.Database.Source)
		break
	case "postgres":
		driver = postgres.Open(cfg.Data.Database.Source)
		break
	case "clickhouse":
		driver = clickhouse.Open(cfg.Data.Database.Source)
		break
	case "sqlite":
		driver = sqlite.Open(cfg.Data.Database.Source)
		break
	case "sqlserver":
		driver = sqlserver.Open(cfg.Data.Database.Source)
		break
	}

	client, err := gorm.Open(driver, &gorm.Config{})
	if err != nil {
		l.Fatalf("failed opening connection to db: %v", err)
		return nil
	}

	return client
}

func NewConsumerHttpClient(cfg *conf.Bootstrap) (v1.ConsumerHTTPClient, error) {
	client, err := http.NewClient(
		context.Background(),
		http.WithEndpoint(cfg.SgConsumer.GetDomain()),
	)
	if err != nil {
		return nil, errors.Wrap(err, "new consumer http client")
	}

	return v1.NewConsumerHTTPClient(client), nil
}

// GetUnavailableCtlConns returns all ctlConnIds from the unavailable set.
func (d *Data) GetUnavailableCtlConns(ctx context.Context) ([]string, error) {
	unavailableKey := "ctlunavailable"
	return d.rdb.SMembers(ctx, unavailableKey).Result()
}

package data

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	"github.com/ClickHouse/clickhouse-go/v2"
	"github.com/go-kratos/kratos/v2/log"
	contextlog "sh_proxy/pkg/log"
)

// IPRecord IP记录结构
type IPRecord struct {
	IP        string    `json:"ip"`
	Supplier  string    `json:"supplier"`
	Location  string    `json:"location"`
	SdkId     string    `json:"sdkId"`
	Timestamp time.Time `json:"timestamp"`
	Host      string    `json:"host"`
	ConnId    string    `json:"connId"`
	CtlConnId string    `json:"ctlConnId"`
}

// ClickHouseBatchWriter ClickHouse批量写入器
type ClickHouseBatchWriter struct {
	log    *contextlog.Helper
	db     *sql.DB
	config *ClickHouseBatchConfig
	
	// 批量缓冲区
	buffer    []*IPRecord
	bufferMux sync.Mutex
	
	// 控制通道
	flushCh   chan struct{}
	stopCh    chan struct{}
	doneCh    chan struct{}
	
	// 统计信息
	totalRecords   int64
	totalBatches   int64
	lastFlushTime  time.Time
}

// ClickHouseBatchConfig 批量写入配置
type ClickHouseBatchConfig struct {
	DSN           string        `yaml:"dsn"`
	BatchSize     int           `yaml:"batch_size"`     // 批量大小，默认10000
	FlushInterval time.Duration `yaml:"flush_interval"` // 刷新间隔，默认1分钟
	TableName     string        `yaml:"table_name"`     // 表名
	MaxRetries    int           `yaml:"max_retries"`    // 最大重试次数
}

// NewClickHouseBatchWriter 创建ClickHouse批量写入器
func NewClickHouseBatchWriter(logger log.Logger, config *ClickHouseBatchConfig) (*ClickHouseBatchWriter, error) {
	// 设置默认配置
	if config.BatchSize <= 0 {
		config.BatchSize = 10000
	}
	if config.FlushInterval <= 0 {
		config.FlushInterval = time.Minute
	}
	if config.TableName == "" {
		config.TableName = "device_ips"
	}
	if config.MaxRetries <= 0 {
		config.MaxRetries = 3
	}
	
	// 连接ClickHouse
	db, err := sql.Open("clickhouse", config.DSN)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to ClickHouse: %w", err)
	}
	
	// 测试连接
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping ClickHouse: %w", err)
	}
	
	writer := &ClickHouseBatchWriter{
		log:           contextlog.NewHelper(log.With(logger, "module", "clickhouse_batch_writer")),
		db:            db,
		config:        config,
		buffer:        make([]*IPRecord, 0, config.BatchSize),
		flushCh:       make(chan struct{}, 1),
		stopCh:        make(chan struct{}),
		doneCh:        make(chan struct{}),
		lastFlushTime: time.Now(),
	}
	
	// 创建表（如果不存在）
	if err := writer.createTableIfNotExists(); err != nil {
		return nil, fmt.Errorf("failed to create table: %w", err)
	}
	
	return writer, nil
}

// Start 启动批量写入器
func (w *ClickHouseBatchWriter) Start() {
	go w.flushWorker()
	w.log.Infow("msg", "ClickHouse批量写入器已启动",
		"batch_size", w.config.BatchSize,
		"flush_interval", w.config.FlushInterval,
		"table_name", w.config.TableName,
	)
}

// Stop 停止批量写入器并刷新所有数据
func (w *ClickHouseBatchWriter) Stop(ctx context.Context) error {
	w.log.Info("正在停止ClickHouse批量写入器...")
	
	// 发送停止信号
	close(w.stopCh)
	
	// 等待工作协程完成或超时
	select {
	case <-w.doneCh:
		w.log.Info("ClickHouse批量写入器已正常停止")
	case <-ctx.Done():
		w.log.Warn("ClickHouse批量写入器停止超时")
	}
	
	// 最后一次刷新剩余数据
	if err := w.flushBuffer(ctx); err != nil {
		w.log.Errorw("msg", "最终刷新失败", "error", err)
		return err
	}
	
	// 关闭数据库连接
	if err := w.db.Close(); err != nil {
		w.log.Errorw("msg", "关闭数据库连接失败", "error", err)
		return err
	}
	
	w.log.Infow("msg", "ClickHouse批量写入器已完全停止",
		"total_records", w.totalRecords,
		"total_batches", w.totalBatches,
	)
	
	return nil
}

// AddRecord 添加IP记录到缓冲区
func (w *ClickHouseBatchWriter) AddRecord(record *IPRecord) {
	w.bufferMux.Lock()
	defer w.bufferMux.Unlock()
	
	w.buffer = append(w.buffer, record)
	
	// 检查是否需要刷新
	if len(w.buffer) >= w.config.BatchSize {
		select {
		case w.flushCh <- struct{}{}:
		default:
			// 通道已满，跳过
		}
	}
}

// flushWorker 刷新工作协程
func (w *ClickHouseBatchWriter) flushWorker() {
	defer close(w.doneCh)
	
	ticker := time.NewTicker(w.config.FlushInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-w.stopCh:
			// 停止信号，退出前最后刷新一次
			w.log.Info("收到停止信号，执行最后刷新")
			if err := w.flushBuffer(context.Background()); err != nil {
				w.log.Errorw("msg", "最后刷新失败", "error", err)
			}
			return
			
		case <-ticker.C:
			// 定时刷新
			if err := w.flushBuffer(context.Background()); err != nil {
				w.log.Errorw("msg", "定时刷新失败", "error", err)
			}
			
		case <-w.flushCh:
			// 缓冲区满，立即刷新
			if err := w.flushBuffer(context.Background()); err != nil {
				w.log.Errorw("msg", "缓冲区刷新失败", "error", err)
			}
		}
	}
}

// flushBuffer 刷新缓冲区到ClickHouse
func (w *ClickHouseBatchWriter) flushBuffer(ctx context.Context) error {
	w.bufferMux.Lock()
	
	if len(w.buffer) == 0 {
		w.bufferMux.Unlock()
		return nil
	}
	
	// 复制缓冲区数据
	records := make([]*IPRecord, len(w.buffer))
	copy(records, w.buffer)
	
	// 清空缓冲区
	w.buffer = w.buffer[:0]
	w.bufferMux.Unlock()
	
	// 执行批量插入
	return w.batchInsert(ctx, records)
}

// batchInsert 批量插入数据
func (w *ClickHouseBatchWriter) batchInsert(ctx context.Context, records []*IPRecord) error {
	if len(records) == 0 {
		return nil
	}
	
	start := time.Now()
	
	// 重试逻辑
	var lastErr error
	for attempt := 0; attempt < w.config.MaxRetries; attempt++ {
		if attempt > 0 {
			w.log.Warnw("msg", "重试批量插入",
				"attempt", attempt+1,
				"records", len(records),
				"error", lastErr,
			)
			time.Sleep(time.Duration(attempt) * time.Second)
		}
		
		if err := w.doInsert(ctx, records); err != nil {
			lastErr = err
			continue
		}
		
		// 插入成功
		w.totalRecords += int64(len(records))
		w.totalBatches++
		w.lastFlushTime = time.Now()
		
		w.log.Infow("msg", "批量插入成功",
			"records", len(records),
			"duration", time.Since(start),
			"total_records", w.totalRecords,
			"total_batches", w.totalBatches,
		)
		
		return nil
	}
	
	w.log.Errorw("msg", "批量插入最终失败",
		"records", len(records),
		"attempts", w.config.MaxRetries,
		"error", lastErr,
	)
	
	return fmt.Errorf("batch insert failed after %d attempts: %w", w.config.MaxRetries, lastErr)
}

// doInsert 执行实际的插入操作
func (w *ClickHouseBatchWriter) doInsert(ctx context.Context, records []*IPRecord) error {
	tx, err := w.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()
	
	stmt, err := tx.PrepareContext(ctx, fmt.Sprintf(`
		INSERT INTO %s (ip, supplier, location, sdk_id, timestamp, host, conn_id, ctl_conn_id)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?)
	`, w.config.TableName))
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()
	
	for _, record := range records {
		if _, err := stmt.ExecContext(ctx,
			record.IP,
			record.Supplier,
			record.Location,
			record.SdkId,
			record.Timestamp,
			record.Host,
			record.ConnId,
			record.CtlConnId,
		); err != nil {
			return fmt.Errorf("failed to execute statement: %w", err)
		}
	}
	
	if err := tx.Commit(); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	
	return nil
}

// createTableIfNotExists 创建表（如果不存在）
func (w *ClickHouseBatchWriter) createTableIfNotExists() error {
	createSQL := fmt.Sprintf(`
		CREATE TABLE IF NOT EXISTS %s (
			ip String,
			supplier String,
			location String,
			sdk_id String,
			timestamp DateTime,
			host String,
			conn_id String,
			ctl_conn_id String,
			date Date DEFAULT toDate(timestamp)
		) ENGINE = MergeTree()
		PARTITION BY date
		ORDER BY (supplier, ip, timestamp)
		SETTINGS index_granularity = 8192
	`, w.config.TableName)
	
	if _, err := w.db.Exec(createSQL); err != nil {
		return fmt.Errorf("failed to create table: %w", err)
	}
	
	w.log.Infow("msg", "ClickHouse表检查完成", "table", w.config.TableName)
	return nil
}

// GetStats 获取统计信息
func (w *ClickHouseBatchWriter) GetStats() map[string]interface{} {
	w.bufferMux.Lock()
	bufferSize := len(w.buffer)
	w.bufferMux.Unlock()
	
	return map[string]interface{}{
		"total_records":    w.totalRecords,
		"total_batches":    w.totalBatches,
		"buffer_size":      bufferSize,
		"last_flush_time":  w.lastFlushTime.Format("2006-01-02 15:04:05"),
		"batch_size_limit": w.config.BatchSize,
		"flush_interval":   w.config.FlushInterval.String(),
	}
}

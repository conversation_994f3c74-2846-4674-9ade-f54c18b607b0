package main

import (
	"flag"
	"fmt"
	"os"

	"github.com/go-kratos/kratos/v2/registry"
	"github.com/go-kratos/kratos/v2/transport/grpc"

	"sh_proxy/app/consumer/internal/server"
	"sh_proxy/pkg/bootstrap"
	"sh_proxy/pkg/utils"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"

	_ "go.uber.org/automaxprocs"
)

var ServiceName = fmt.Sprintf("%s.sh_proxy.consumer.service", utils.GetNamespace())

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name string
	// Version is the version of the compiled software.
	Version string
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()

	Service = bootstrap.NewServiceInfo(
		ServiceName,
		"1.0.0",
		"",
	)
)

func init() {
	flag.StringVar(&flagconf, "conf", "../../configs", "config path, eg: -conf config.yaml")
}

func newApp(logger log.Logger, hs *http.Server, gs *grpc.Server, cs *server.ConsumerServer, reg registry.Registrar, lm *server.LifecycleManager) *kratos.App {
	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		kratos.Server(
			hs,
			gs,
			cs,
			lm,
		),
		kratos.Registrar(reg),
	)
}

func main() {
	flag.Parse()
	// load configs
	bc := LoadBootstrapConfig(flagconf)
	if bc == nil {
		panic("load config failed")
	}

	// init logger
	logger := bootstrap.NewLoggerProvider(&bootstrap.LoggerConf{
		Type: bootstrap.LoggerType(bc.Logger.Type),
		Zap: &bootstrap.ZapConfig{
			Filename:     bc.Logger.Zap.Filename,
			Level:        bc.Logger.Zap.Level,
			MaxSize:      bc.Logger.Zap.MaxSize,
			MaxAge:       bc.Logger.Zap.MaxAge,
			MaxBackups:   bc.Logger.Zap.MaxBackups,
			SplitByLevel: bc.Logger.Zap.SplitByLevel,
			LogDir:       bc.Logger.Zap.LogDir,
		},
	}, Service)

	var reg registry.Registrar
	// if utils.IsK8sEnv() {
	// init registrar
	// reg = NewRegistry(bc.Registry)
	// }

	app, cleanup, err := wireApp(bc.Server, bc.Data, bc, logger, reg)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}

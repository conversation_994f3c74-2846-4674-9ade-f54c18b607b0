// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/registry"
	"sh_proxy/app/consumer/internal/biz"
	"sh_proxy/app/consumer/internal/conf"
	"sh_proxy/app/consumer/internal/data"
	"sh_proxy/app/consumer/internal/server"
	"sh_proxy/app/consumer/internal/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, bootstrap *conf.Bootstrap, logger log.Logger, registrar registry.Registrar) (*kratos.App, func(), error) {
	mqConn := data.NewRabbitMQClient(bootstrap, logger)
	client, err := data.NewMongoClient(bootstrap, logger)
	if err != nil {
		return nil, nil, err
	}
	db := data.NewGormClient(bootstrap, logger)
	consumerHTTPClient, err := data.NewConsumerHttpClient(bootstrap)
	if err != nil {
		return nil, nil, err
	}
	clickHouseBatchWriterV2 := data.NewClickHouseBatchWriterV2(logger, db)
	dataData, cleanup, err := data.NewData(confData, logger, mqConn, client, db, consumerHTTPClient, clickHouseBatchWriterV2)
	if err != nil {
		return nil, nil, err
	}
	ipInfoRepo := data.NewIPInfoRepo(dataData, bootstrap, logger)
	eventLogRepo := data.NewEventLogRepo(dataData, bootstrap, logger)
	consumerUsecase := biz.NewConsumerUsecase(dataData, ipInfoRepo, eventLogRepo)
	consumerWebService := service.NewConsumerWebService(consumerUsecase)
	httpServer := server.NewHTTPServer(confServer, consumerWebService, logger)
	grpcServer := server.NewGRPCServer(confServer, consumerWebService, logger)
	locationMappingService := data.NewLocationMappingService(logger, dataData)
	deviceRepo := data.NewDeviceRepo(dataData, locationMappingService)
	userUsecase := biz.NewUserUsecase(logger, dataData, ipInfoRepo, deviceRepo, eventLogRepo)
	consumerService, err := service.NewConsumerService(userUsecase, dataData, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	consumerServer := server.NewConsumerServer(consumerService, logger)
	syncService := service.NewSyncService(logger, client, db, confData)
	lifecycleManager := server.NewLifecycleManager(logger, clickHouseBatchWriterV2, syncService)
	app := newApp(logger, httpServer, grpcServer, consumerServer, registrar, lifecycleManager)
	return app, func() {
		cleanup()
	}, nil
}

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: consumer/v1/consumer.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type IPInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ip            string                 `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IPInfoReq) Reset() {
	*x = IPInfoReq{}
	mi := &file_consumer_v1_consumer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IPInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPInfoReq) ProtoMessage() {}

func (x *IPInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_consumer_v1_consumer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPInfoReq.ProtoReflect.Descriptor instead.
func (*IPInfoReq) Descriptor() ([]byte, []int) {
	return file_consumer_v1_consumer_proto_rawDescGZIP(), []int{0}
}

func (x *IPInfoReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type IPInfoResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ip            string                 `protobuf:"bytes,1,opt,name=ip,proto3" json:"ip,omitempty"`
	Country       string                 `protobuf:"bytes,2,opt,name=country,proto3" json:"country,omitempty"`
	CountryCode   string                 `protobuf:"bytes,3,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	Region        string                 `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	RegionName    string                 `protobuf:"bytes,5,opt,name=region_name,json=regionName,proto3" json:"region_name,omitempty"`
	City          string                 `protobuf:"bytes,6,opt,name=city,proto3" json:"city,omitempty"`
	District      string                 `protobuf:"bytes,7,opt,name=district,proto3" json:"district,omitempty"`
	Zip           string                 `protobuf:"bytes,8,opt,name=zip,proto3" json:"zip,omitempty"`
	Lat           float32                `protobuf:"fixed32,9,opt,name=lat,proto3" json:"lat,omitempty"`
	Lon           float32                `protobuf:"fixed32,10,opt,name=lon,proto3" json:"lon,omitempty"`
	Timezone      string                 `protobuf:"bytes,11,opt,name=timezone,proto3" json:"timezone,omitempty"`
	Isp           string                 `protobuf:"bytes,12,opt,name=isp,proto3" json:"isp,omitempty"`
	Offset        int64                  `protobuf:"varint,13,opt,name=offset,proto3" json:"offset,omitempty"`
	Currency      string                 `protobuf:"bytes,14,opt,name=currency,proto3" json:"currency,omitempty"`
	Proxy         bool                   `protobuf:"varint,15,opt,name=proxy,proto3" json:"proxy,omitempty"`
	Hosting       bool                   `protobuf:"varint,16,opt,name=hosting,proto3" json:"hosting,omitempty"`
	Asname        string                 `protobuf:"bytes,17,opt,name=asname,proto3" json:"asname,omitempty"`
	As            string                 `protobuf:"bytes,18,opt,name=as,proto3" json:"as,omitempty"`
	Mobile        bool                   `protobuf:"varint,19,opt,name=mobile,proto3" json:"mobile,omitempty"`
	IsNew         bool                   `protobuf:"varint,20,opt,name=is_new,json=isNew,proto3" json:"is_new,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IPInfoResp) Reset() {
	*x = IPInfoResp{}
	mi := &file_consumer_v1_consumer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IPInfoResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPInfoResp) ProtoMessage() {}

func (x *IPInfoResp) ProtoReflect() protoreflect.Message {
	mi := &file_consumer_v1_consumer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPInfoResp.ProtoReflect.Descriptor instead.
func (*IPInfoResp) Descriptor() ([]byte, []int) {
	return file_consumer_v1_consumer_proto_rawDescGZIP(), []int{1}
}

func (x *IPInfoResp) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *IPInfoResp) GetCountry() string {
	if x != nil {
		return x.Country
	}
	return ""
}

func (x *IPInfoResp) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *IPInfoResp) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *IPInfoResp) GetRegionName() string {
	if x != nil {
		return x.RegionName
	}
	return ""
}

func (x *IPInfoResp) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *IPInfoResp) GetDistrict() string {
	if x != nil {
		return x.District
	}
	return ""
}

func (x *IPInfoResp) GetZip() string {
	if x != nil {
		return x.Zip
	}
	return ""
}

func (x *IPInfoResp) GetLat() float32 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *IPInfoResp) GetLon() float32 {
	if x != nil {
		return x.Lon
	}
	return 0
}

func (x *IPInfoResp) GetTimezone() string {
	if x != nil {
		return x.Timezone
	}
	return ""
}

func (x *IPInfoResp) GetIsp() string {
	if x != nil {
		return x.Isp
	}
	return ""
}

func (x *IPInfoResp) GetOffset() int64 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *IPInfoResp) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *IPInfoResp) GetProxy() bool {
	if x != nil {
		return x.Proxy
	}
	return false
}

func (x *IPInfoResp) GetHosting() bool {
	if x != nil {
		return x.Hosting
	}
	return false
}

func (x *IPInfoResp) GetAsname() string {
	if x != nil {
		return x.Asname
	}
	return ""
}

func (x *IPInfoResp) GetAs() string {
	if x != nil {
		return x.As
	}
	return ""
}

func (x *IPInfoResp) GetMobile() bool {
	if x != nil {
		return x.Mobile
	}
	return false
}

func (x *IPInfoResp) GetIsNew() bool {
	if x != nil {
		return x.IsNew
	}
	return false
}

type EventSaveReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	SdkId         string                 `protobuf:"bytes,2,opt,name=sdk_id,json=sdkId,proto3" json:"sdk_id,omitempty"`
	SdkIp         string                 `protobuf:"bytes,3,opt,name=sdk_ip,json=sdkIp,proto3" json:"sdk_ip,omitempty"`
	Host          string                 `protobuf:"bytes,4,opt,name=host,proto3" json:"host,omitempty"`
	Supplier      string                 `protobuf:"bytes,5,opt,name=supplier,proto3" json:"supplier,omitempty"`
	ConnId        string                 `protobuf:"bytes,6,opt,name=conn_id,json=connId,proto3" json:"conn_id,omitempty"`
	Timestamp     string                 `protobuf:"bytes,7,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Meta          string                 `protobuf:"bytes,8,opt,name=meta,proto3" json:"meta,omitempty"`
	CtlConnId     string                 `protobuf:"bytes,9,opt,name=ctl_conn_id,json=ctlConnId,proto3" json:"ctl_conn_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventSaveReq) Reset() {
	*x = EventSaveReq{}
	mi := &file_consumer_v1_consumer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventSaveReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventSaveReq) ProtoMessage() {}

func (x *EventSaveReq) ProtoReflect() protoreflect.Message {
	mi := &file_consumer_v1_consumer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventSaveReq.ProtoReflect.Descriptor instead.
func (*EventSaveReq) Descriptor() ([]byte, []int) {
	return file_consumer_v1_consumer_proto_rawDescGZIP(), []int{2}
}

func (x *EventSaveReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EventSaveReq) GetSdkId() string {
	if x != nil {
		return x.SdkId
	}
	return ""
}

func (x *EventSaveReq) GetSdkIp() string {
	if x != nil {
		return x.SdkIp
	}
	return ""
}

func (x *EventSaveReq) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *EventSaveReq) GetSupplier() string {
	if x != nil {
		return x.Supplier
	}
	return ""
}

func (x *EventSaveReq) GetConnId() string {
	if x != nil {
		return x.ConnId
	}
	return ""
}

func (x *EventSaveReq) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *EventSaveReq) GetMeta() string {
	if x != nil {
		return x.Meta
	}
	return ""
}

func (x *EventSaveReq) GetCtlConnId() string {
	if x != nil {
		return x.CtlConnId
	}
	return ""
}

type EventSaveResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventSaveResp) Reset() {
	*x = EventSaveResp{}
	mi := &file_consumer_v1_consumer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventSaveResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventSaveResp) ProtoMessage() {}

func (x *EventSaveResp) ProtoReflect() protoreflect.Message {
	mi := &file_consumer_v1_consumer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventSaveResp.ProtoReflect.Descriptor instead.
func (*EventSaveResp) Descriptor() ([]byte, []int) {
	return file_consumer_v1_consumer_proto_rawDescGZIP(), []int{3}
}

type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_consumer_v1_consumer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_consumer_v1_consumer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_consumer_v1_consumer_proto_rawDescGZIP(), []int{4}
}

var File_consumer_v1_consumer_proto protoreflect.FileDescriptor

const file_consumer_v1_consumer_proto_rawDesc = "" +
	"\n" +
	"\x1aconsumer/v1/consumer.proto\x12\vconsumer.v1\x1a\x1cgoogle/api/annotations.proto\"\x1b\n" +
	"\tIPInfoReq\x12\x0e\n" +
	"\x02ip\x18\x01 \x01(\tR\x02ip\"\xe1\x03\n" +
	"\n" +
	"IPInfoResp\x12\x0e\n" +
	"\x02ip\x18\x01 \x01(\tR\x02ip\x12\x18\n" +
	"\acountry\x18\x02 \x01(\tR\acountry\x12!\n" +
	"\fcountry_code\x18\x03 \x01(\tR\vcountryCode\x12\x16\n" +
	"\x06region\x18\x04 \x01(\tR\x06region\x12\x1f\n" +
	"\vregion_name\x18\x05 \x01(\tR\n" +
	"regionName\x12\x12\n" +
	"\x04city\x18\x06 \x01(\tR\x04city\x12\x1a\n" +
	"\bdistrict\x18\a \x01(\tR\bdistrict\x12\x10\n" +
	"\x03zip\x18\b \x01(\tR\x03zip\x12\x10\n" +
	"\x03lat\x18\t \x01(\x02R\x03lat\x12\x10\n" +
	"\x03lon\x18\n" +
	" \x01(\x02R\x03lon\x12\x1a\n" +
	"\btimezone\x18\v \x01(\tR\btimezone\x12\x10\n" +
	"\x03isp\x18\f \x01(\tR\x03isp\x12\x16\n" +
	"\x06offset\x18\r \x01(\x03R\x06offset\x12\x1a\n" +
	"\bcurrency\x18\x0e \x01(\tR\bcurrency\x12\x14\n" +
	"\x05proxy\x18\x0f \x01(\bR\x05proxy\x12\x18\n" +
	"\ahosting\x18\x10 \x01(\bR\ahosting\x12\x16\n" +
	"\x06asname\x18\x11 \x01(\tR\x06asname\x12\x0e\n" +
	"\x02as\x18\x12 \x01(\tR\x02as\x12\x16\n" +
	"\x06mobile\x18\x13 \x01(\bR\x06mobile\x12\x15\n" +
	"\x06is_new\x18\x14 \x01(\bR\x05isNew\"\xeb\x01\n" +
	"\fEventSaveReq\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x15\n" +
	"\x06sdk_id\x18\x02 \x01(\tR\x05sdkId\x12\x15\n" +
	"\x06sdk_ip\x18\x03 \x01(\tR\x05sdkIp\x12\x12\n" +
	"\x04host\x18\x04 \x01(\tR\x04host\x12\x1a\n" +
	"\bsupplier\x18\x05 \x01(\tR\bsupplier\x12\x17\n" +
	"\aconn_id\x18\x06 \x01(\tR\x06connId\x12\x1c\n" +
	"\ttimestamp\x18\a \x01(\tR\ttimestamp\x12\x12\n" +
	"\x04meta\x18\b \x01(\tR\x04meta\x12\x1e\n" +
	"\vctl_conn_id\x18\t \x01(\tR\tctlConnId\"\x0f\n" +
	"\rEventSaveResp\"\a\n" +
	"\x05Empty2\xf8\x01\n" +
	"\bConsumer\x12A\n" +
	"\x06Health\x12\x12.consumer.v1.Empty\x1a\x12.consumer.v1.Empty\"\x0f\x82\xd3\xe4\x93\x02\t\x12\a/health\x12M\n" +
	"\x06IPInfo\x12\x16.consumer.v1.IPInfoReq\x1a\x17.consumer.v1.IPInfoResp\"\x12\x82\xd3\xe4\x93\x02\f\x12\n" +
	"/v1/ipinfo\x12Z\n" +
	"\tEventSave\x12\x19.consumer.v1.EventSaveReq\x1a\x1a.consumer.v1.EventSaveResp\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/v1/event-saveBL\n" +
	"\x1adev.kratos.api.consumer.v1B\x0fConsumerProtoV1P\x01Z\x1bsh_proxy/api/consumer/v1;v1b\x06proto3"

var (
	file_consumer_v1_consumer_proto_rawDescOnce sync.Once
	file_consumer_v1_consumer_proto_rawDescData []byte
)

func file_consumer_v1_consumer_proto_rawDescGZIP() []byte {
	file_consumer_v1_consumer_proto_rawDescOnce.Do(func() {
		file_consumer_v1_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_consumer_v1_consumer_proto_rawDesc), len(file_consumer_v1_consumer_proto_rawDesc)))
	})
	return file_consumer_v1_consumer_proto_rawDescData
}

var file_consumer_v1_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_consumer_v1_consumer_proto_goTypes = []any{
	(*IPInfoReq)(nil),     // 0: consumer.v1.IPInfoReq
	(*IPInfoResp)(nil),    // 1: consumer.v1.IPInfoResp
	(*EventSaveReq)(nil),  // 2: consumer.v1.EventSaveReq
	(*EventSaveResp)(nil), // 3: consumer.v1.EventSaveResp
	(*Empty)(nil),         // 4: consumer.v1.Empty
}
var file_consumer_v1_consumer_proto_depIdxs = []int32{
	4, // 0: consumer.v1.Consumer.Health:input_type -> consumer.v1.Empty
	0, // 1: consumer.v1.Consumer.IPInfo:input_type -> consumer.v1.IPInfoReq
	2, // 2: consumer.v1.Consumer.EventSave:input_type -> consumer.v1.EventSaveReq
	4, // 3: consumer.v1.Consumer.Health:output_type -> consumer.v1.Empty
	1, // 4: consumer.v1.Consumer.IPInfo:output_type -> consumer.v1.IPInfoResp
	3, // 5: consumer.v1.Consumer.EventSave:output_type -> consumer.v1.EventSaveResp
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_consumer_v1_consumer_proto_init() }
func file_consumer_v1_consumer_proto_init() {
	if File_consumer_v1_consumer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_consumer_v1_consumer_proto_rawDesc), len(file_consumer_v1_consumer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_consumer_v1_consumer_proto_goTypes,
		DependencyIndexes: file_consumer_v1_consumer_proto_depIdxs,
		MessageInfos:      file_consumer_v1_consumer_proto_msgTypes,
	}.Build()
	File_consumer_v1_consumer_proto = out.File
	file_consumer_v1_consumer_proto_goTypes = nil
	file_consumer_v1_consumer_proto_depIdxs = nil
}

# 概览
用户服务为所有sdk设备的连接，断连，寻址提供服务
# 功能
1. 接收连接事件，查询 ip 归属，存储为在线设备
2. 接收断连事件，从在线设备中踢出
3. 接收 gw 请求，根据请求目标地区返回可用在线设备
# 连接事件
### 请求信息
sdk_id, sdk_ip,connid host(当前sdk连接建立在哪个 pod)
### 返回信息
message,err
### 处理
1. 根据 ip 查询归属地,先查询本地库，不存在再查询外部接口(ip-api/ip2location)
2. 根据归属地存储该 sdk 到在线设备列表
3. 记录日志
### 技术点
1. ip 本地库中心式存储，两个 region 不重复请求(同一个 sdkid 会同时触发两个 region 的连接事件)
2. 在线设备存储 key 设计
```text
1. 满足通过 location 快速查询
2. 满足通过 sdkid 快速删除(离线时移除)
3. 快速写入 
```
3. 连接日志异步写入(统计设备在线时长)

# 断连事件
### 请求信息
sdk_id, sdk_ip
### 返回信息
message,err
### 处理
1. 根据 sdkid 从在线设备中移除

### gw 路由
### 请求信息
location,authUser,session,host,port
### 返回信息
host,sdkid,sdkip
### 处理
1. 根据 location 匹配设备
2. 根据 authuser_sessid, location 匹配设备 


1. 确保sdk 设备能被使用到
2. 不能分配已经掉线的设备

3. ip 归属，确保混播能用
4. ip 归属查询(考虑异步)
5. 鉴权(考虑ip加白)
6. nacos，日志，pprof，metrics

## 结构设计
目前的 key 设计
目前的 key 设计 mixed_weight:{supplier}， 所有 location 的 ip 数量为 score
location:{supplier}:{location} 为 ip 列表
device:{supplier}:{ip} 为 device 信息

建立连接




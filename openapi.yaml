# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /health:
        get:
            tags:
                - Consumer
            description: Sends a greeting
            operationId: Consumer_Health
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/consumer.v1.Empty'
    /v1/event:
        post:
            tags:
                - User
            operationId: User_Event
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user.v1.EventRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.EventReply'
    /v1/event-save:
        get:
            tags:
                - Consumer
            description: 保存事件
            operationId: Consumer_EventSave
            parameters:
                - name: name
                  in: query
                  schema:
                    type: string
                - name: sdkId
                  in: query
                  schema:
                    type: string
                - name: sdkIp
                  in: query
                  schema:
                    type: string
                - name: host
                  in: query
                  schema:
                    type: string
                - name: supplier
                  in: query
                  schema:
                    type: string
                - name: connId
                  in: query
                  schema:
                    type: string
                - name: timestamp
                  in: query
                  schema:
                    type: string
                - name: meta
                  in: query
                  schema:
                    type: string
                - name: ctlConnId
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/consumer.v1.EventSaveResp'
    /v1/ipinfo:
        get:
            tags:
                - Consumer
            description: 查询 ipinfo
            operationId: Consumer_IPInfo
            parameters:
                - name: ip
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/consumer.v1.IPInfoResp'
    /v1/mixed-weight:
        get:
            tags:
                - User
            operationId: User_MixedWeight
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.MixedWeightReply'
    /v1/route:
        post:
            tags:
                - User
            operationId: User_Route
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user.v1.RouteRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.RouteReply'
    /v1/route-by-ip:
        post:
            tags:
                - User
            operationId: User_RouteByIp
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user.v1.RouteByIpRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.RouteByIpReply'
components:
    schemas:
        consumer.v1.Empty:
            type: object
            properties: {}
        consumer.v1.EventSaveResp:
            type: object
            properties: {}
        consumer.v1.IPInfoResp:
            type: object
            properties:
                ip:
                    type: string
                country:
                    type: string
                countryCode:
                    type: string
                region:
                    type: string
                regionName:
                    type: string
                city:
                    type: string
                district:
                    type: string
                zip:
                    type: string
                lat:
                    type: number
                    format: float
                lon:
                    type: number
                    format: float
                timezone:
                    type: string
                isp:
                    type: string
                offset:
                    type: string
                currency:
                    type: string
                proxy:
                    type: boolean
                hosting:
                    type: boolean
                asname:
                    type: string
                as:
                    type: string
                mobile:
                    type: boolean
                isNew:
                    type: boolean
        user.v1.Empty:
            type: object
            properties: {}
        user.v1.EventReply:
            type: object
            properties:
                message:
                    type: string
        user.v1.EventRequest:
            type: object
            properties:
                name:
                    type: string
                sdkId:
                    type: string
                sdkIp:
                    type: string
                host:
                    type: string
                supplier:
                    type: string
                meta:
                    type: string
                connId:
                    type: string
                ctlConnId:
                    type: string
        user.v1.IpList:
            type: object
            properties:
                host:
                    type: string
                skdId:
                    type: string
                sdkIp:
                    type: string
                connId:
                    type: string
                ctlConnId:
                    type: string
        user.v1.MixedWeightReply:
            type: object
            properties:
                mixedWeight:
                    type: string
        user.v1.RouteByIpReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/user.v1.IpList'
        user.v1.RouteByIpRequest:
            type: object
            properties:
                supplier:
                    type: string
                ip:
                    type: string
                skdId:
                    type: string
        user.v1.RouteReply:
            type: object
            properties:
                host:
                    type: string
                skdId:
                    type: string
                sdkIp:
                    type: string
                connId:
                    type: string
                ctlConnId:
                    type: string
        user.v1.RouteRequest:
            type: object
            properties:
                location:
                    type: string
                authUser:
                    type: string
                session:
                    type: string
                host:
                    type: string
                port:
                    type: string
                supplier:
                    type: string
tags:
    - name: Consumer
    - name: User
      description: The greeting service definition.
